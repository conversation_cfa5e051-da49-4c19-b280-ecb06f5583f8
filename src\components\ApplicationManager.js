import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tabs,
  Tab,
  Card,
  CardContent,
  Grid,
  Chip,
  Avatar,
  TextField,
  InputAdornment,
  Menu,
  MenuItem,
  Divider,
  Alert,
  Tooltip,
  CircularProgress,
  Stack,
  Switch,
  FormControlLabel,
  LinearProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  validateInput,
  validateFileOperation,
  createRetryHandler,
  checkDeviceConnection,
  logError,
  getErrorMessage
} from '../utils/errorHandler';
import { useNotifications } from './NotificationSystem';
import {
  Apps,
  Android,
  Search,
  GetApp,
  Delete,
  Backup,
  Launch,
  Info,
  Refresh,
  Upload,
  Settings,
  Security,
  Storage,
  MoreVert,
  ExpandMore,
  SystemUpdate,
  Warning,
  CheckCircle,
  Error,
  FilterList,
  Sort,
  Clear
} from '@mui/icons-material';

function ApplicationManager({ selectedDevice, onShowNotification }) {
  const [installedApps, setInstalledApps] = useState([]);
  const [systemApps, setSystemApps] = useState([]);
  const [currentTab, setCurrentTab] = useState(0);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedApp, setSelectedApp] = useState(null);
  const [showAppDetails, setShowAppDetails] = useState(false);
  const [showInstallDialog, setShowInstallDialog] = useState(false);
  const [contextMenu, setContextMenu] = useState(null);
  const [contextApp, setContextApp] = useState(null);
  const [showSystemApps, setShowSystemApps] = useState(false);
  const [sortBy, setSortBy] = useState('name');
  const [filterBy, setFilterBy] = useState('all');
  const [apkFile, setApkFile] = useState(null);
  const [installProgress, setInstallProgress] = useState(null);
  const [retryCount, setRetryCount] = useState(0);
  const [connectionError, setConnectionError] = useState(null);

  // Use enhanced notification system
  const { showError, showSuccess, showWarning, showInfo } = useNotifications();

  useEffect(() => {
    if (selectedDevice) {
      loadApplications();
    }
  }, [selectedDevice]);

  const loadApplications = createRetryHandler(async () => {
    if (!selectedDevice) return;

    try {
      // Validate inputs
      validateInput.deviceId(selectedDevice.id);

      setLoading(true);
      setConnectionError(null);

      // Check device connection
      const connectionCheck = await checkDeviceConnection(selectedDevice.id);
      if (!connectionCheck.connected) {
        throw new Error(`Device not connected: ${connectionCheck.error}`);
      }

      // Check if app API is available
      if (!window.electronAPI?.app?.getInstalledApps) {
        throw new Error('Application API not available');
      }

      const [userApps, sysApps] = await Promise.all([
        window.electronAPI.app.getInstalledApps(selectedDevice.id, false) || [],
        showSystemApps ? window.electronAPI.app.getInstalledApps(selectedDevice.id, true) || [] : []
      ]);

      setInstalledApps(userApps);
      setSystemApps(sysApps);
      setRetryCount(0); // Reset retry count on success

      if (retryCount > 0) {
        showSuccess('Applications loaded successfully');
      }
    } catch (error) {
      logError(error, 'Loading applications', { deviceId: selectedDevice.id });
      setConnectionError(error.message);

      showError(error, 'Loading applications', {
        retryAction: () => loadApplications(),
        suggestions: [
          'Check if the device is connected',
          'Ensure USB debugging is enabled',
          'Try reconnecting the device',
          'Restart ADB server if needed'
        ]
      });

      setRetryCount(prev => prev + 1);
      throw error; // Re-throw for retry handler
    } finally {
      setLoading(false);
    }
  }, 3, 2000);

  const handleInstallApk = async () => {
    if (!apkFile || !selectedDevice) return;

    try {
      setInstallProgress({ status: 'installing', progress: 0 });

      const result = await window.electronAPI.app.installApk(selectedDevice.id, apkFile.path);

      if (result.success) {
        onShowNotification('Application installed successfully', 'success');
        loadApplications();
      } else {
        onShowNotification(`Installation failed: ${result.error}`, 'error');
      }
    } catch (error) {
      onShowNotification('Failed to install application', 'error');
    } finally {
      setInstallProgress(null);
      setShowInstallDialog(false);
      setApkFile(null);
    }
  };

  const handleUninstallApp = async (packageName) => {
    if (!window.confirm(`Are you sure you want to uninstall ${packageName}?`)) {
      return;
    }

    try {
      const result = await window.electronAPI.app.uninstallApp(selectedDevice.id, packageName);

      if (result.success) {
        onShowNotification('Application uninstalled successfully', 'success');
        loadApplications();
      } else {
        onShowNotification(`Uninstall failed: ${result.error}`, 'error');
      }
    } catch (error) {
      onShowNotification('Failed to uninstall application', 'error');
    }
  };

  const handleBackupApp = async (packageName) => {
    try {
      const result = await window.electronAPI.app.backupApp(selectedDevice.id, packageName);

      if (result.success) {
        onShowNotification(`Application backed up to: ${result.path}`, 'success');
      } else {
        onShowNotification(`Backup failed: ${result.error}`, 'error');
      }
    } catch (error) {
      onShowNotification('Failed to backup application', 'error');
    }
  };

  const handleLaunchApp = async (packageName) => {
    try {
      const result = await window.electronAPI.app.launchApp(selectedDevice.id, packageName);

      if (result.success) {
        onShowNotification('Application launched', 'success');
      } else {
        onShowNotification(`Launch failed: ${result.error}`, 'error');
      }
    } catch (error) {
      onShowNotification('Failed to launch application', 'error');
    }
  };

  const handleContextMenu = (event, app) => {
    event.preventDefault();
    setContextMenu({
      mouseX: event.clientX - 2,
      mouseY: event.clientY - 4,
    });
    setContextApp(app);
  };

  const closeContextMenu = () => {
    setContextMenu(null);
    setContextApp(null);
  };

  const getAppIcon = (app) => {
    if (app.icon) {
      return <Avatar src={app.icon} sx={{ width: 40, height: 40 }} />;
    }
    return <Avatar sx={{ width: 40, height: 40, bgcolor: 'primary.main' }}>
      <Apps />
    </Avatar>;
  };

  const getAppTypeChip = (app) => {
    if (app.isSystemApp) {
      return <Chip label="System" size="small" color="secondary" />;
    }
    if (app.isUpdatedSystemApp) {
      return <Chip label="System (Updated)" size="small" color="warning" />;
    }
    return <Chip label="User" size="small" color="primary" />;
  };

  const formatFileSize = (bytes) => {
    if (!bytes) return '0 B';
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
  };

  const filterAndSortApps = (apps) => {
    let filtered = apps.filter(app => {
      const matchesSearch = app.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           app.packageName.toLowerCase().includes(searchQuery.toLowerCase());

      if (filterBy === 'all') return matchesSearch;
      if (filterBy === 'user') return matchesSearch && !app.isSystemApp;
      if (filterBy === 'system') return matchesSearch && app.isSystemApp;
      if (filterBy === 'updated') return matchesSearch && app.isUpdatedSystemApp;

      return matchesSearch;
    });

    return filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'size':
          return (b.size || 0) - (a.size || 0);
        case 'installDate':
          return new Date(b.installDate || 0) - new Date(a.installDate || 0);
        case 'updateDate':
          return new Date(b.updateDate || 0) - new Date(a.updateDate || 0);
        default:
          return 0;
      }
    });
  };

  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (file && file.name.endsWith('.apk')) {
      setApkFile(file);
      setShowInstallDialog(true);
    } else {
      onShowNotification('Please select a valid APK file', 'warning');
    }
    event.target.value = '';
  };

  const renderAppItem = (app) => (
    <ListItem
      key={app.packageName}
      onContextMenu={(e) => handleContextMenu(e, app)}
      sx={{
        border: 1,
        borderColor: 'divider',
        borderRadius: 1,
        mb: 1,
        '&:hover': {
          backgroundColor: 'action.hover',
        },
      }}
    >
      <ListItemIcon>
        {getAppIcon(app)}
      </ListItemIcon>
      <ListItemText
        primary={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Typography variant="subtitle2" noWrap>
              {app.name}
            </Typography>
            {getAppTypeChip(app)}
            {app.isDebuggable && (
              <Chip label="Debug" size="small" color="warning" />
            )}
          </Box>
        }
        secondary={
          <Box>
            <Typography variant="caption" color="text.secondary">
              {app.packageName} • v{app.versionName} ({app.versionCode})
            </Typography>
            <br />
            <Typography variant="caption" color="text.secondary">
              {formatFileSize(app.size)} • Target SDK: {app.targetSdk}
            </Typography>
            {app.permissions && app.permissions.length > 0 && (
              <Typography variant="caption" color="text.secondary">
                <br />
                {app.permissions.length} permissions
              </Typography>
            )}
          </Box>
        }
      />
      <ListItemSecondaryAction>
        <Box sx={{ display: 'flex', gap: 0.5 }}>
          <Tooltip title="Launch">
            <IconButton
              size="small"
              onClick={() => handleLaunchApp(app.packageName)}
              disabled={!app.launchable}
            >
              <Launch />
            </IconButton>
          </Tooltip>
          <Tooltip title="App Info">
            <IconButton
              size="small"
              onClick={() => {
                setSelectedApp(app);
                setShowAppDetails(true);
              }}
            >
              <Info />
            </IconButton>
          </Tooltip>
          <Tooltip title="More options">
            <IconButton
              size="small"
              onClick={(e) => handleContextMenu(e, app)}
            >
              <MoreVert />
            </IconButton>
          </Tooltip>
        </Box>
      </ListItemSecondaryAction>
    </ListItem>
  );

  if (!selectedDevice) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Android sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
        <Typography variant="h6" gutterBottom>
          No Device Selected
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Please select a device from the Device Manager to manage applications.
        </Typography>
      </Box>
    );
  }

  const allApps = [...installedApps, ...(showSystemApps ? systemApps : [])];
  const filteredApps = filterAndSortApps(allApps);

  return (
    <Box sx={{ p: 3, height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h4" component="h1">
          Application Manager
        </Typography>
        <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
          <input
            type="file"
            accept=".apk"
            onChange={handleFileSelect}
            style={{ display: 'none' }}
            id="apk-file-input"
          />
          <label htmlFor="apk-file-input">
            <Button
              component="span"
              startIcon={<Upload />}
              variant="contained"
            >
              Install APK
            </Button>
          </label>
          <Button
            startIcon={<Refresh />}
            onClick={loadApplications}
            disabled={loading}
          >
            Refresh
          </Button>
        </Box>
      </Box>

      {/* Statistics */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="primary">
                {installedApps.length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                User Apps
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="secondary">
                {systemApps.length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                System Apps
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="success.main">
                {allApps.filter(app => app.isUpdatedSystemApp).length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Updated System
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="warning.main">
                {allApps.filter(app => app.isDebuggable).length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Debug Apps
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Controls */}
      <Paper sx={{ p: 2, mb: 2 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              size="small"
              placeholder="Search applications..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search />
                  </InputAdornment>
                ),
                endAdornment: searchQuery && (
                  <InputAdornment position="end">
                    <IconButton size="small" onClick={() => setSearchQuery('')}>
                      <Clear />
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              select
              fullWidth
              size="small"
              label="Filter"
              value={filterBy}
              onChange={(e) => setFilterBy(e.target.value)}
            >
              <MenuItem value="all">All Apps</MenuItem>
              <MenuItem value="user">User Apps</MenuItem>
              <MenuItem value="system">System Apps</MenuItem>
              <MenuItem value="updated">Updated System</MenuItem>
            </TextField>
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              select
              fullWidth
              size="small"
              label="Sort by"
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
            >
              <MenuItem value="name">Name</MenuItem>
              <MenuItem value="size">Size</MenuItem>
              <MenuItem value="installDate">Install Date</MenuItem>
              <MenuItem value="updateDate">Update Date</MenuItem>
            </TextField>
          </Grid>
          <Grid item xs={12} md={4}>
            <FormControlLabel
              control={
                <Switch
                  checked={showSystemApps}
                  onChange={(e) => {
                    setShowSystemApps(e.target.checked);
                    if (e.target.checked) {
                      loadApplications();
                    }
                  }}
                />
              }
              label="Show system apps"
            />
          </Grid>
        </Grid>
      </Paper>

      {/* App List */}
      <Paper sx={{ flexGrow: 1, overflow: 'auto' }}>
        {loading ? (
          <Box sx={{ p: 2 }}>
            <LinearProgress />
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              Loading applications...
            </Typography>
          </Box>
        ) : filteredApps.length === 0 ? (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <Apps sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" gutterBottom>
              No Applications Found
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {searchQuery ? 'No apps match your search criteria.' : 'No applications are installed on this device.'}
            </Typography>
          </Box>
        ) : (
          <List sx={{ p: 2 }}>
            {filteredApps.map(app => renderAppItem(app))}
          </List>
        )}
      </Paper>

      {/* Context Menu */}
      <Menu
        open={contextMenu !== null}
        onClose={closeContextMenu}
        anchorReference="anchorPosition"
        anchorPosition={
          contextMenu !== null
            ? { top: contextMenu.mouseY, left: contextMenu.mouseX }
            : undefined
        }
      >
        {contextApp && (
          <>
            <MenuItem onClick={() => { handleLaunchApp(contextApp.packageName); closeContextMenu(); }}>
              <ListItemIcon><Launch fontSize="small" /></ListItemIcon>
              <ListItemText>Launch</ListItemText>
            </MenuItem>
            <MenuItem onClick={() => { setSelectedApp(contextApp); setShowAppDetails(true); closeContextMenu(); }}>
              <ListItemIcon><Info fontSize="small" /></ListItemIcon>
              <ListItemText>App Info</ListItemText>
            </MenuItem>
            <Divider />
            <MenuItem onClick={() => { handleBackupApp(contextApp.packageName); closeContextMenu(); }}>
              <ListItemIcon><Backup fontSize="small" /></ListItemIcon>
              <ListItemText>Backup APK</ListItemText>
            </MenuItem>
            <Divider />
            {!contextApp.isSystemApp && (
              <MenuItem onClick={() => { handleUninstallApp(contextApp.packageName); closeContextMenu(); }} sx={{ color: 'error.main' }}>
                <ListItemIcon><Delete fontSize="small" color="error" /></ListItemIcon>
                <ListItemText>Uninstall</ListItemText>
              </MenuItem>
            )}
          </>
        )}
      </Menu>

      {/* Install APK Dialog */}
      <Dialog open={showInstallDialog} onClose={() => setShowInstallDialog(false)}>
        <DialogTitle>Install APK</DialogTitle>
        <DialogContent>
          {apkFile && (
            <Box>
              <Typography variant="subtitle2" gutterBottom>
                Selected File:
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                {apkFile.name}
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Size: {formatFileSize(apkFile.size)}
              </Typography>

              {installProgress && (
                <Box sx={{ mt: 2 }}>
                  <Typography variant="body2" gutterBottom>
                    Installing application...
                  </Typography>
                  <LinearProgress />
                </Box>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowInstallDialog(false)} disabled={installProgress}>
            Cancel
          </Button>
          <Button onClick={handleInstallApk} variant="contained" disabled={installProgress}>
            Install
          </Button>
        </DialogActions>
      </Dialog>

      {/* App Details Dialog */}
      <Dialog open={showAppDetails} onClose={() => setShowAppDetails(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            {selectedApp && getAppIcon(selectedApp)}
            <Box>
              <Typography variant="h6">{selectedApp?.name}</Typography>
              <Typography variant="body2" color="text.secondary">
                {selectedApp?.packageName}
              </Typography>
            </Box>
          </Box>
        </DialogTitle>
        <DialogContent>
          {selectedApp && (
            <Box>
              <Grid container spacing={2} sx={{ mb: 3 }}>
                <Grid item xs={6}>
                  <Typography variant="subtitle2" gutterBottom>Version</Typography>
                  <Typography variant="body2" color="text.secondary">
                    {selectedApp.versionName} ({selectedApp.versionCode})
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="subtitle2" gutterBottom>Size</Typography>
                  <Typography variant="body2" color="text.secondary">
                    {formatFileSize(selectedApp.size)}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="subtitle2" gutterBottom>Target SDK</Typography>
                  <Typography variant="body2" color="text.secondary">
                    {selectedApp.targetSdk}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="subtitle2" gutterBottom>Min SDK</Typography>
                  <Typography variant="body2" color="text.secondary">
                    {selectedApp.minSdk}
                  </Typography>
                </Grid>
                {selectedApp.installDate && (
                  <Grid item xs={6}>
                    <Typography variant="subtitle2" gutterBottom>Install Date</Typography>
                    <Typography variant="body2" color="text.secondary">
                      {new Date(selectedApp.installDate).toLocaleString()}
                    </Typography>
                  </Grid>
                )}
                {selectedApp.updateDate && (
                  <Grid item xs={6}>
                    <Typography variant="subtitle2" gutterBottom>Update Date</Typography>
                    <Typography variant="body2" color="text.secondary">
                      {new Date(selectedApp.updateDate).toLocaleString()}
                    </Typography>
                  </Grid>
                )}
              </Grid>

              <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                {getAppTypeChip(selectedApp)}
                {selectedApp.isDebuggable && (
                  <Chip label="Debuggable" size="small" color="warning" />
                )}
                {selectedApp.hasCode && (
                  <Chip label="Has Code" size="small" color="info" />
                )}
                {selectedApp.allowBackup && (
                  <Chip label="Backup Allowed" size="small" color="success" />
                )}
              </Box>

              {selectedApp.permissions && selectedApp.permissions.length > 0 && (
                <Accordion>
                  <AccordionSummary expandIcon={<ExpandMore />}>
                    <Typography variant="subtitle2">
                      Permissions ({selectedApp.permissions.length})
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <List dense>
                      {selectedApp.permissions.map((permission, index) => (
                        <ListItem key={index}>
                          <ListItemIcon>
                            <Security fontSize="small" />
                          </ListItemIcon>
                          <ListItemText
                            primary={permission.name}
                            secondary={permission.description}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </AccordionDetails>
                </Accordion>
              )}

              {selectedApp.activities && selectedApp.activities.length > 0 && (
                <Accordion>
                  <AccordionSummary expandIcon={<ExpandMore />}>
                    <Typography variant="subtitle2">
                      Activities ({selectedApp.activities.length})
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <List dense>
                      {selectedApp.activities.map((activity, index) => (
                        <ListItem key={index}>
                          <ListItemText primary={activity} />
                        </ListItem>
                      ))}
                    </List>
                  </AccordionDetails>
                </Accordion>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowAppDetails(false)}>Close</Button>
          {selectedApp && (
            <>
              <Button onClick={() => handleBackupApp(selectedApp.packageName)}>
                Backup
              </Button>
              <Button onClick={() => handleLaunchApp(selectedApp.packageName)} variant="contained">
                Launch
              </Button>
            </>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default ApplicationManager;
