.App {
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  padding: 20px;
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* File browser styles */
.file-browser {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.file-list {
  flex: 1;
  overflow: auto;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  border-bottom: 1px solid #eee;
  cursor: pointer;
  transition: background-color 0.2s;
}

.file-item:hover {
  background-color: #f5f5f5;
}

.file-item.selected {
  background-color: #e3f2fd;
}

.file-icon {
  margin-right: 12px;
  width: 24px;
  height: 24px;
}

.file-info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.file-name {
  font-weight: 500;
  color: #333;
}

.file-size {
  color: #666;
  font-size: 0.875rem;
}

.file-date {
  color: #999;
  font-size: 0.75rem;
}

/* Transfer progress styles */
.transfer-item {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  background: white;
}

.transfer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.transfer-progress {
  margin: 8px 0;
}

.transfer-files {
  font-size: 0.875rem;
  color: #666;
}

/* Device card styles */
.device-card {
  border: 1px solid #ddd;
  border-radius: 12px;
  padding: 20px;
  margin: 12px;
  background: white;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: all 0.2s;
  cursor: pointer;
}

.device-card:hover {
  box-shadow: 0 4px 16px rgba(0,0,0,0.15);
  transform: translateY(-2px);
}

.device-card.selected {
  border-color: #1976d2;
  box-shadow: 0 4px 16px rgba(25,118,210,0.2);
}

.device-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.device-avatar {
  width: 48px;
  height: 48px;
  margin-right: 16px;
  background: #1976d2;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.device-info h3 {
  margin: 0 0 4px 0;
  font-size: 1.25rem;
  color: #333;
}

.device-info p {
  margin: 0;
  color: #666;
  font-size: 0.875rem;
}

.device-status {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 12px;
}

.status-chip {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-connected {
  background: #e8f5e8;
  color: #2e7d32;
}

.status-disconnected {
  background: #ffebee;
  color: #c62828;
}

/* Responsive design */
@media (max-width: 768px) {
  .device-card {
    margin: 8px;
    padding: 16px;
  }
  
  .file-item {
    padding: 12px 16px;
  }
  
  .file-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
