// Error types and categories
export const ErrorTypes = {
  NETWORK: 'network',
  DEVICE: 'device',
  FILE_SYSTEM: 'file_system',
  PERMISSION: 'permission',
  VALIDATION: 'validation',
  TRANSFER: 'transfer',
  APPLICATION: 'application',
  SETTINGS: 'settings',
  UNKNOWN: 'unknown'
};

export const ErrorSeverity = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
};

// Error classification
export const classifyError = (error) => {
  const message = error.message?.toLowerCase() || '';
  
  if (message.includes('network') || message.includes('connection') || message.includes('timeout')) {
    return { type: ErrorTypes.NETWORK, severity: ErrorSeverity.HIGH };
  }
  
  if (message.includes('device') || message.includes('adb') || message.includes('unauthorized')) {
    return { type: ErrorTypes.DEVICE, severity: ErrorSeverity.HIGH };
  }
  
  if (message.includes('permission') || message.includes('access denied') || message.includes('forbidden')) {
    return { type: ErrorTypes.PERMISSION, severity: ErrorSeverity.MEDIUM };
  }
  
  if (message.includes('file') || message.includes('directory') || message.includes('path')) {
    return { type: ErrorTypes.FILE_SYSTEM, severity: ErrorSeverity.MEDIUM };
  }
  
  if (message.includes('transfer') || message.includes('upload') || message.includes('download')) {
    return { type: ErrorTypes.TRANSFER, severity: ErrorSeverity.MEDIUM };
  }
  
  if (message.includes('app') || message.includes('package') || message.includes('install')) {
    return { type: ErrorTypes.APPLICATION, severity: ErrorSeverity.MEDIUM };
  }
  
  if (message.includes('settings') || message.includes('config')) {
    return { type: ErrorTypes.SETTINGS, severity: ErrorSeverity.LOW };
  }
  
  return { type: ErrorTypes.UNKNOWN, severity: ErrorSeverity.MEDIUM };
};

// User-friendly error messages
export const getErrorMessage = (error, context = '') => {
  const { type } = classifyError(error);
  const originalMessage = error.message || 'An unknown error occurred';
  
  const messages = {
    [ErrorTypes.NETWORK]: {
      title: 'Connection Error',
      message: 'Unable to connect to the device. Please check your connection and try again.',
      suggestions: [
        'Ensure the device is connected via USB',
        'Check if USB debugging is enabled',
        'Try restarting ADB server',
        'Reconnect the device'
      ]
    },
    [ErrorTypes.DEVICE]: {
      title: 'Device Error',
      message: 'There was an issue communicating with the device.',
      suggestions: [
        'Make sure the device is unlocked',
        'Check if USB debugging is authorized',
        'Try reconnecting the device',
        'Restart the device if necessary'
      ]
    },
    [ErrorTypes.PERMISSION]: {
      title: 'Permission Error',
      message: 'Access denied. You may not have the required permissions.',
      suggestions: [
        'Check file/folder permissions',
        'Run the application as administrator if needed',
        'Ensure the device allows file access',
        'Try accessing a different location'
      ]
    },
    [ErrorTypes.FILE_SYSTEM]: {
      title: 'File System Error',
      message: 'There was an issue accessing the file or directory.',
      suggestions: [
        'Check if the file/folder exists',
        'Verify you have read/write permissions',
        'Ensure there is enough disk space',
        'Try refreshing the file list'
      ]
    },
    [ErrorTypes.TRANSFER]: {
      title: 'Transfer Error',
      message: 'File transfer failed. The operation could not be completed.',
      suggestions: [
        'Check available storage space',
        'Ensure stable device connection',
        'Try transferring smaller files',
        'Retry the operation'
      ]
    },
    [ErrorTypes.APPLICATION]: {
      title: 'Application Error',
      message: 'There was an issue with the application operation.',
      suggestions: [
        'Check if the APK file is valid',
        'Ensure the app is compatible',
        'Try installing from a different source',
        'Check device storage space'
      ]
    },
    [ErrorTypes.SETTINGS]: {
      title: 'Settings Error',
      message: 'Unable to save or load settings.',
      suggestions: [
        'Check application permissions',
        'Try resetting to default settings',
        'Restart the application',
        'Check available disk space'
      ]
    },
    [ErrorTypes.UNKNOWN]: {
      title: 'Unexpected Error',
      message: originalMessage,
      suggestions: [
        'Try the operation again',
        'Restart the application',
        'Check the console for more details',
        'Contact support if the issue persists'
      ]
    }
  };
  
  const errorInfo = messages[type] || messages[ErrorTypes.UNKNOWN];
  
  return {
    ...errorInfo,
    originalMessage,
    context,
    type,
    timestamp: new Date().toISOString()
  };
};

// Retry logic
export const createRetryHandler = (operation, maxRetries = 3, delay = 1000) => {
  return async (...args) => {
    let lastError;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation(...args);
      } catch (error) {
        lastError = error;
        const { type, severity } = classifyError(error);
        
        // Don't retry certain types of errors
        if (type === ErrorTypes.PERMISSION || type === ErrorTypes.VALIDATION) {
          throw error;
        }
        
        // Don't retry on last attempt
        if (attempt === maxRetries) {
          break;
        }
        
        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, delay * attempt));
      }
    }
    
    throw lastError;
  };
};

// Input validation helpers
export const validateInput = {
  required: (value, fieldName) => {
    if (!value || (typeof value === 'string' && value.trim() === '')) {
      throw new Error(`${fieldName} is required`);
    }
  },
  
  path: (path) => {
    if (!path || typeof path !== 'string') {
      throw new Error('Invalid path provided');
    }
    
    // Check for dangerous path patterns
    const dangerousPatterns = ['../', '..\\', '<', '>', '|', '*', '?'];
    if (dangerousPatterns.some(pattern => path.includes(pattern))) {
      throw new Error('Path contains invalid characters');
    }
  },
  
  fileName: (name) => {
    if (!name || typeof name !== 'string') {
      throw new Error('Invalid file name provided');
    }
    
    // Check for invalid file name characters
    const invalidChars = /[<>:"/\\|?*]/;
    if (invalidChars.test(name)) {
      throw new Error('File name contains invalid characters');
    }
    
    if (name.length > 255) {
      throw new Error('File name is too long');
    }
  },
  
  packageName: (packageName) => {
    if (!packageName || typeof packageName !== 'string') {
      throw new Error('Invalid package name provided');
    }
    
    const packagePattern = /^[a-zA-Z][a-zA-Z0-9_]*(\.[a-zA-Z][a-zA-Z0-9_]*)*$/;
    if (!packagePattern.test(packageName)) {
      throw new Error('Invalid package name format');
    }
  },
  
  deviceId: (deviceId) => {
    if (!deviceId || typeof deviceId !== 'string') {
      throw new Error('Invalid device ID provided');
    }
  }
};

// Error logging
export const logError = (error, context = '', additionalData = {}) => {
  const errorInfo = {
    message: error.message,
    stack: error.stack,
    context,
    timestamp: new Date().toISOString(),
    userAgent: navigator.userAgent,
    url: window.location.href,
    ...additionalData
  };
  
  console.error('Application Error:', errorInfo);
  
  // In production, you might want to send this to an error tracking service
  // Example: sendToErrorTrackingService(errorInfo);
};

// Safe async operation wrapper
export const safeAsync = (operation, fallback = null) => {
  return async (...args) => {
    try {
      return await operation(...args);
    } catch (error) {
      logError(error, `Safe async operation: ${operation.name}`);
      return fallback;
    }
  };
};

// Device connection checker
export const checkDeviceConnection = async (deviceId) => {
  try {
    if (!window.electronAPI?.device?.getInfo) {
      throw new Error('Device API not available');
    }
    
    const info = await window.electronAPI.device.getInfo(deviceId);
    return { connected: true, info };
  } catch (error) {
    return { connected: false, error: error.message };
  }
};

// File operation validators
export const validateFileOperation = {
  upload: (files, destination) => {
    validateInput.required(files, 'Files');
    validateInput.required(destination, 'Destination');
    validateInput.path(destination);
    
    if (!Array.isArray(files) || files.length === 0) {
      throw new Error('No files selected for upload');
    }
  },
  
  download: (files, destination) => {
    validateInput.required(files, 'Files');
    validateInput.required(destination, 'Destination');
    validateInput.path(destination);
    
    if (!Array.isArray(files) || files.length === 0) {
      throw new Error('No files selected for download');
    }
  },
  
  delete: (files) => {
    validateInput.required(files, 'Files');
    
    if (!Array.isArray(files) || files.length === 0) {
      throw new Error('No files selected for deletion');
    }
  },
  
  rename: (oldName, newName) => {
    validateInput.required(oldName, 'Current name');
    validateInput.required(newName, 'New name');
    validateInput.fileName(newName);
    
    if (oldName === newName) {
      throw new Error('New name must be different from current name');
    }
  }
};
