import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  IconButton,
  Chip,
  Alert,
  Tooltip,
  Switch,
  FormControlLabel,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Divider,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  LinearProgress,
  Tab,
  Tabs
} from '@mui/material';
import {
  Build,
  Android,
  Screenshot,
  BugReport,
  Refresh,
  PowerSettingsNew,
  Wifi,
  Bluetooth,
  VolumeUp,
  Brightness6,
  Storage,
  Memory,
  BatteryFull,
  Thermostat,
  NetworkCheck,
  Security,
  Info,
  Terminal,
  Save,
  Clear,
  Search,
  FilterList,
  ExpandMore,
  PlayArrow,
  Pause,
  Stop,
  Download,
  Settings,
  PhoneAndroid,
  DeveloperMode
} from '@mui/icons-material';

function DeviceTools({ selectedDevice, onShowNotification }) {
  const [currentTab, setCurrentTab] = useState(0);
  const [screenshot, setScreenshot] = useState(null);
  const [showScreenshotDialog, setShowScreenshotDialog] = useState(false);
  const [logcatLogs, setLogcatLogs] = useState([]);
  const [logcatRunning, setLogcatRunning] = useState(false);
  const [logcatFilter, setLogcatFilter] = useState('');
  const [logcatLevel, setLogcatLevel] = useState('V');
  const [deviceInfo, setDeviceInfo] = useState(null);
  const [shellCommand, setShellCommand] = useState('');
  const [shellOutput, setShellOutput] = useState('');
  const [shellHistory, setShellHistory] = useState([]);
  const [loading, setLoading] = useState(false);
  const logcatRef = useRef(null);
  const shellRef = useRef(null);

  useEffect(() => {
    if (selectedDevice) {
      loadDeviceInfo();
    }
  }, [selectedDevice]);

  useEffect(() => {
    if (logcatRef.current) {
      logcatRef.current.scrollTop = logcatRef.current.scrollHeight;
    }
  }, [logcatLogs]);

  const loadDeviceInfo = async () => {
    if (!selectedDevice) return;

    try {
      setLoading(true);
      const info = await window.electronAPI.device?.getDeviceInfo?.(selectedDevice.id) || {};
      setDeviceInfo(info);
    } catch (error) {
      console.error('Failed to load device info:', error);
      onShowNotification('Failed to load device information', 'error');
    } finally {
      setLoading(false);
    }
  };

  const takeScreenshot = async () => {
    if (!selectedDevice) return;

    try {
      setLoading(true);
      const result = await window.electronAPI.device.takeScreenshot(selectedDevice.id);

      if (result.success) {
        setScreenshot(result.imagePath);
        setShowScreenshotDialog(true);
        onShowNotification('Screenshot captured successfully', 'success');
      } else {
        onShowNotification(`Screenshot failed: ${result.error}`, 'error');
      }
    } catch (error) {
      onShowNotification('Failed to take screenshot', 'error');
    } finally {
      setLoading(false);
    }
  };

  const saveScreenshot = async () => {
    if (!screenshot) return;

    try {
      const result = await window.electronAPI.device.saveScreenshot(screenshot);

      if (result.success) {
        onShowNotification(`Screenshot saved to: ${result.path}`, 'success');
      } else {
        onShowNotification('Failed to save screenshot', 'error');
      }
    } catch (error) {
      onShowNotification('Failed to save screenshot', 'error');
    }
  };

  const startLogcat = async () => {
    if (!selectedDevice) return;

    try {
      setLogcatRunning(true);
      setLogcatLogs([]);

      // Set up logcat stream
      const result = await window.electronAPI.device.startLogcat(selectedDevice.id, {
        level: logcatLevel,
        filter: logcatFilter
      });

      if (result.success) {
        onShowNotification('Logcat started', 'info');

        // Listen for logcat updates
        window.electronAPI.device.onLogcatData?.((data) => {
          setLogcatLogs(prev => [...prev, ...data.logs]);
        });
      } else {
        setLogcatRunning(false);
        onShowNotification(`Failed to start logcat: ${result.error}`, 'error');
      }
    } catch (error) {
      setLogcatRunning(false);
      onShowNotification('Failed to start logcat', 'error');
    }
  };

  const stopLogcat = async () => {
    try {
      await window.electronAPI.device.stopLogcat(selectedDevice.id);
      setLogcatRunning(false);
      onShowNotification('Logcat stopped', 'info');
    } catch (error) {
      onShowNotification('Failed to stop logcat', 'error');
    }
  };

  const clearLogcat = () => {
    setLogcatLogs([]);
  };

  const saveLogcat = async () => {
    try {
      const result = await window.electronAPI.device.saveLogcat(logcatLogs);

      if (result.success) {
        onShowNotification(`Logcat saved to: ${result.path}`, 'success');
      } else {
        onShowNotification('Failed to save logcat', 'error');
      }
    } catch (error) {
      onShowNotification('Failed to save logcat', 'error');
    }
  };

  const executeShellCommand = async () => {
    if (!shellCommand.trim() || !selectedDevice) return;

    try {
      const result = await window.electronAPI.device.executeShell(selectedDevice.id, shellCommand);

      const output = `$ ${shellCommand}\n${result.output || result.error || 'Command executed'}\n\n`;
      setShellOutput(prev => prev + output);

      // Add to history
      setShellHistory(prev => {
        const newHistory = [shellCommand, ...prev.filter(cmd => cmd !== shellCommand)];
        return newHistory.slice(0, 50); // Keep last 50 commands
      });

      setShellCommand('');

      if (result.error) {
        onShowNotification('Command executed with errors', 'warning');
      }
    } catch (error) {
      const output = `$ ${shellCommand}\nError: ${error.message}\n\n`;
      setShellOutput(prev => prev + output);
      onShowNotification('Failed to execute command', 'error');
    }
  };

  const rebootDevice = async (mode = 'normal') => {
    if (!window.confirm(`Are you sure you want to reboot the device${mode !== 'normal' ? ` into ${mode} mode` : ''}?`)) {
      return;
    }

    try {
      const result = await window.electronAPI.device.reboot(selectedDevice.id, mode);

      if (result.success) {
        onShowNotification(`Device rebooting${mode !== 'normal' ? ` into ${mode} mode` : ''}`, 'info');
      } else {
        onShowNotification(`Reboot failed: ${result.error}`, 'error');
      }
    } catch (error) {
      onShowNotification('Failed to reboot device', 'error');
    }
  };

  const toggleWifi = async () => {
    try {
      const result = await window.electronAPI.device.toggleWifi(selectedDevice.id);

      if (result.success) {
        onShowNotification(`WiFi ${result.enabled ? 'enabled' : 'disabled'}`, 'success');
        loadDeviceInfo();
      } else {
        onShowNotification(`Failed to toggle WiFi: ${result.error}`, 'error');
      }
    } catch (error) {
      onShowNotification('Failed to toggle WiFi', 'error');
    }
  };

  const getLogLevelColor = (level) => {
    switch (level) {
      case 'E': return 'error';
      case 'W': return 'warning';
      case 'I': return 'info';
      case 'D': return 'primary';
      case 'V': return 'default';
      default: return 'default';
    }
  };

  const filteredLogs = logcatLogs.filter(log =>
    !logcatFilter || log.message.toLowerCase().includes(logcatFilter.toLowerCase()) ||
    log.tag.toLowerCase().includes(logcatFilter.toLowerCase())
  );

  if (!selectedDevice) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Android sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
        <Typography variant="h6" gutterBottom>
          No Device Selected
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Please select a device from the Device Manager to access device tools.
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3, height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h4" component="h1">
          Device Tools
        </Typography>
        <Button
          startIcon={<Refresh />}
          onClick={loadDeviceInfo}
          disabled={loading}
        >
          Refresh
        </Button>
      </Box>

      {/* Tabs */}
      <Paper sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
        <Tabs
          value={currentTab}
          onChange={(e, newValue) => setCurrentTab(newValue)}
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          <Tab icon={<Screenshot />} label="Screenshot" iconPosition="start" />
          <Tab icon={<BugReport />} label="Logcat" iconPosition="start" />
          <Tab icon={<Terminal />} label="Shell" iconPosition="start" />
          <Tab icon={<Settings />} label="Device Control" iconPosition="start" />
          <Tab icon={<Info />} label="Device Info" iconPosition="start" />
        </Tabs>

        {/* Tab Content */}
        <Box sx={{ flexGrow: 1, overflow: 'auto', p: 2 }}>
          {/* Screenshot Tab */}
          {currentTab === 0 && (
            <Box>
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        Screen Capture
                      </Typography>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Capture a screenshot of the device screen
                      </Typography>
                    </CardContent>
                    <CardActions>
                      <Button
                        startIcon={<Screenshot />}
                        onClick={takeScreenshot}
                        disabled={loading}
                        variant="contained"
                      >
                        Take Screenshot
                      </Button>
                    </CardActions>
                  </Card>
                </Grid>
              </Grid>
            </Box>
          )}

          {/* Logcat Tab */}
          {currentTab === 1 && (
            <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
              {/* Logcat Controls */}
              <Paper sx={{ p: 2, mb: 2 }}>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={12} md={3}>
                    <FormControl fullWidth size="small">
                      <InputLabel>Log Level</InputLabel>
                      <Select
                        value={logcatLevel}
                        onChange={(e) => setLogcatLevel(e.target.value)}
                        label="Log Level"
                      >
                        <MenuItem value="V">Verbose</MenuItem>
                        <MenuItem value="D">Debug</MenuItem>
                        <MenuItem value="I">Info</MenuItem>
                        <MenuItem value="W">Warning</MenuItem>
                        <MenuItem value="E">Error</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <TextField
                      fullWidth
                      size="small"
                      placeholder="Filter logs..."
                      value={logcatFilter}
                      onChange={(e) => setLogcatFilter(e.target.value)}
                    />
                  </Grid>
                  <Grid item xs={12} md={5}>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      {!logcatRunning ? (
                        <Button
                          startIcon={<PlayArrow />}
                          onClick={startLogcat}
                          variant="contained"
                          color="success"
                        >
                          Start
                        </Button>
                      ) : (
                        <Button
                          startIcon={<Stop />}
                          onClick={stopLogcat}
                          variant="contained"
                          color="error"
                        >
                          Stop
                        </Button>
                      )}
                      <Button
                        startIcon={<Clear />}
                        onClick={clearLogcat}
                      >
                        Clear
                      </Button>
                      <Button
                        startIcon={<Save />}
                        onClick={saveLogcat}
                        disabled={logcatLogs.length === 0}
                      >
                        Save
                      </Button>
                    </Box>
                  </Grid>
                </Grid>
              </Paper>

              {/* Logcat Output */}
              <Paper
                ref={logcatRef}
                sx={{
                  flexGrow: 1,
                  p: 1,
                  overflow: 'auto',
                  backgroundColor: 'grey.900',
                  color: 'common.white',
                  fontFamily: 'monospace',
                  fontSize: '0.8rem'
                }}
              >
                {filteredLogs.length === 0 ? (
                  <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', mt: 4 }}>
                    {logcatRunning ? 'Waiting for logs...' : 'Click Start to begin capturing logs'}
                  </Typography>
                ) : (
                  filteredLogs.map((log, index) => (
                    <Box key={index} sx={{ mb: 0.5, display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                      <Chip
                        label={log.level}
                        size="small"
                        color={getLogLevelColor(log.level)}
                        sx={{ minWidth: 30, fontSize: '0.7rem' }}
                      />
                      <Typography variant="caption" sx={{ color: 'grey.400', minWidth: 60 }}>
                        {log.time}
                      </Typography>
                      <Typography variant="caption" sx={{ color: 'cyan', minWidth: 100 }}>
                        {log.tag}
                      </Typography>
                      <Typography variant="caption" sx={{ wordBreak: 'break-all' }}>
                        {log.message}
                      </Typography>
                    </Box>
                  ))
                )}
              </Paper>
            </Box>
          )}

          {/* Shell Tab */}
          {currentTab === 2 && (
            <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
              {/* Shell Output */}
              <Paper
                ref={shellRef}
                sx={{
                  flexGrow: 1,
                  p: 2,
                  mb: 2,
                  overflow: 'auto',
                  backgroundColor: 'grey.900',
                  color: 'common.white',
                  fontFamily: 'monospace',
                  fontSize: '0.9rem',
                  minHeight: 300
                }}
              >
                {shellOutput || 'Shell ready. Enter commands below.'}
              </Paper>

              {/* Shell Input */}
              <Box sx={{ display: 'flex', gap: 1 }}>
                <TextField
                  fullWidth
                  placeholder="Enter shell command..."
                  value={shellCommand}
                  onChange={(e) => setShellCommand(e.target.value)}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      executeShellCommand();
                    }
                  }}
                  InputProps={{
                    startAdornment: <Typography sx={{ mr: 1, color: 'text.secondary' }}>$</Typography>
                  }}
                />
                <Button
                  onClick={executeShellCommand}
                  disabled={!shellCommand.trim()}
                  variant="contained"
                >
                  Execute
                </Button>
                <Button
                  onClick={() => setShellOutput('')}
                >
                  Clear
                </Button>
              </Box>

              {/* Command History */}
              {shellHistory.length > 0 && (
                <Accordion sx={{ mt: 2 }}>
                  <AccordionSummary expandIcon={<ExpandMore />}>
                    <Typography variant="subtitle2">Command History</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <List dense>
                      {shellHistory.map((cmd, index) => (
                        <ListItem
                          key={index}
                          button
                          onClick={() => setShellCommand(cmd)}
                        >
                          <ListItemIcon>
                            <Terminal fontSize="small" />
                          </ListItemIcon>
                          <ListItemText primary={cmd} />
                        </ListItem>
                      ))}
                    </List>
                  </AccordionDetails>
                </Accordion>
              )}
            </Box>
          )}

          {/* Device Control Tab */}
          {currentTab === 3 && (
            <Box>
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        Power Management
                      </Typography>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Reboot or power off the device
                      </Typography>
                    </CardContent>
                    <CardActions>
                      <Button
                        startIcon={<PowerSettingsNew />}
                        onClick={() => rebootDevice('normal')}
                        color="warning"
                      >
                        Reboot
                      </Button>
                      <Button
                        startIcon={<PowerSettingsNew />}
                        onClick={() => rebootDevice('recovery')}
                        color="error"
                      >
                        Recovery
                      </Button>
                      <Button
                        startIcon={<PowerSettingsNew />}
                        onClick={() => rebootDevice('bootloader')}
                        color="error"
                      >
                        Bootloader
                      </Button>
                    </CardActions>
                  </Card>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        Connectivity
                      </Typography>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Manage device connectivity settings
                      </Typography>
                    </CardContent>
                    <CardActions>
                      <Button
                        startIcon={<Wifi />}
                        onClick={toggleWifi}
                      >
                        Toggle WiFi
                      </Button>
                    </CardActions>
                  </Card>
                </Grid>
              </Grid>
            </Box>
          )}

          {/* Device Info Tab */}
          {currentTab === 4 && (
            <Box>
              {loading ? (
                <LinearProgress />
              ) : deviceInfo ? (
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <Card>
                      <CardContent>
                        <Typography variant="h6" gutterBottom>
                          Device Information
                        </Typography>
                        <List dense>
                          <ListItem>
                            <ListItemIcon><PhoneAndroid /></ListItemIcon>
                            <ListItemText primary="Model" secondary={deviceInfo.model} />
                          </ListItem>
                          <ListItem>
                            <ListItemIcon><Android /></ListItemIcon>
                            <ListItemText primary="Android Version" secondary={deviceInfo.androidVersion} />
                          </ListItem>
                          <ListItem>
                            <ListItemIcon><Security /></ListItemIcon>
                            <ListItemText primary="API Level" secondary={deviceInfo.apiLevel} />
                          </ListItem>
                          <ListItem>
                            <ListItemIcon><DeveloperMode /></ListItemIcon>
                            <ListItemText primary="Serial" secondary={deviceInfo.serial} />
                          </ListItem>
                        </List>
                      </CardContent>
                    </Card>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Card>
                      <CardContent>
                        <Typography variant="h6" gutterBottom>
                          Hardware Information
                        </Typography>
                        <List dense>
                          <ListItem>
                            <ListItemIcon><Memory /></ListItemIcon>
                            <ListItemText primary="RAM" secondary={deviceInfo.totalMemory} />
                          </ListItem>
                          <ListItem>
                            <ListItemIcon><Storage /></ListItemIcon>
                            <ListItemText primary="Storage" secondary={deviceInfo.totalStorage} />
                          </ListItem>
                          <ListItem>
                            <ListItemIcon><BatteryFull /></ListItemIcon>
                            <ListItemText primary="Battery" secondary={`${deviceInfo.batteryLevel}%`} />
                          </ListItem>
                          <ListItem>
                            <ListItemIcon><Thermostat /></ListItemIcon>
                            <ListItemText primary="Temperature" secondary={deviceInfo.temperature} />
                          </ListItem>
                        </List>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>
              ) : (
                <Alert severity="info">
                  Device information not available. Click Refresh to try again.
                </Alert>
              )}
            </Box>
          )}
        </Box>
      </Paper>

      {/* Screenshot Dialog */}
      <Dialog
        open={showScreenshotDialog}
        onClose={() => setShowScreenshotDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Screenshot</DialogTitle>
        <DialogContent>
          {screenshot && (
            <Box sx={{ textAlign: 'center' }}>
              <img
                src={`file://${screenshot}`}
                alt="Device Screenshot"
                style={{
                  maxWidth: '100%',
                  maxHeight: '500px',
                  border: '1px solid #ccc',
                  borderRadius: '8px'
                }}
              />
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowScreenshotDialog(false)}>
            Close
          </Button>
          <Button
            onClick={saveScreenshot}
            startIcon={<Save />}
            variant="contained"
          >
            Save
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default DeviceTools;
