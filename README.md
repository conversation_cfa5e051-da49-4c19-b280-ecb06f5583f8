# Android Manager

A comprehensive desktop application for managing Android devices, built with Electron and React.

## 🚀 Features

- **📱 Device Management**: Connect, disconnect, and monitor multiple Android devices
- **📁 File Browser**: Dual-pane interface for browsing device and PC files
- **🔄 File Transfer**: Multi-threaded file transfers with progress tracking
- **📱 App Management**: Install, uninstall, and backup Android applications
- **🛠️ Device Tools**: Screenshots, logcat viewer, and device control
- **🌍 Unicode Support**: Full support for international file names and characters
- **🎨 Modern UI**: Material Design interface with dark/light themes

## 📦 Installation

### Option 1: Download Pre-built Release (Recommended)
1. Go to the [Releases](https://github.com/your-repo/android-manager/releases) page
2. Download the installer for your operating system:
   - **Windows**: `Android-Manager-Setup-1.0.0.exe`
   - **macOS**: `Android-Manager-1.0.0.dmg`
   - **Linux**: `Android-Manager-1.0.0.AppImage`
3. Run the installer and follow the setup instructions
4. Launch Android Manager from your applications menu

### Option 2: Build from Source
```bash
# Clone the repository
git clone https://github.com/your-repo/android-manager.git
cd android-manager

# Install dependencies (this will automatically download ADB binaries)
npm install

# Run in development mode
npm run dev

# Or build for production
npm run build:electron
```

## 🔧 No Additional Setup Required!

**Android Manager comes with ADB (Android Debug Bridge) built-in!** 

Unlike other Android management tools, you don't need to:
- ❌ Install Android SDK
- ❌ Download platform-tools separately  
- ❌ Configure PATH variables
- ❌ Worry about ADB versions

The application automatically:
- ✅ Downloads the latest ADB binaries during installation
- ✅ Uses the correct ADB version for your operating system
- ✅ Handles all ADB configuration internally
- ✅ Works out of the box on Windows, macOS, and Linux

## 📱 Device Setup

To use Android Manager with your device:

1. **Enable Developer Options** on your Android device:
   - Go to Settings → About Phone
   - Tap "Build Number" 7 times
   - Developer Options will appear in Settings

2. **Enable USB Debugging**:
   - Go to Settings → Developer Options
   - Turn on "USB Debugging"

3. **Connect your device** via USB cable

4. **Allow USB Debugging** when prompted on your device

5. **Launch Android Manager** - your device should appear automatically!

## 🖥️ System Requirements

- **Windows**: Windows 10 or later (64-bit)
- **macOS**: macOS 10.14 or later
- **Linux**: Ubuntu 18.04+ or equivalent (64-bit)
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 500MB free space
- **USB**: USB 2.0 or higher port for device connection

## 🛠️ Development

### Prerequisites
- Node.js 16 or later
- npm or yarn

### Development Commands
```bash
# Install dependencies and download ADB binaries
npm install

# Start development server
npm run dev

# Verify ADB installation
npm run verify-adb

# Build for all platforms
npm run build:electron

# Build for specific platform
npm run build:win    # Windows
npm run build:mac    # macOS  
npm run build:linux  # Linux
```

### Project Structure
```
android-manager/
├── src/
│   ├── main/           # Electron main process
│   ├── components/     # React components
│   ├── context/        # React context providers
│   └── App.js         # Main React app
├── resources/
│   └── adb/           # ADB binaries (auto-downloaded)
├── scripts/           # Build and utility scripts
└── package.json       # Dependencies and scripts
```

## 🔍 Troubleshooting

### Device Not Detected
1. Ensure USB debugging is enabled on your device
2. Try a different USB cable or port
3. Check if your device appears in Device Manager (Windows) or System Information (macOS)
4. Restart the ADB service: Settings → Advanced → Restart ADB

### Permission Issues (Linux/macOS)
```bash
# Add your user to the plugdev group (Linux)
sudo usermod -a -G plugdev $USER

# Set up udev rules for Android devices (Linux)
sudo apt-get install android-sdk-platform-tools-common
```

### ADB Issues
The application includes built-in ADB, but if you encounter issues:
```bash
# Verify ADB installation
npm run verify-adb

# Re-download ADB binaries
npm run download-adb
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📞 Support

If you encounter any issues or have questions:
1. Check the [Troubleshooting](#-troubleshooting) section
2. Search existing [Issues](https://github.com/your-repo/android-manager/issues)
3. Create a new issue with detailed information about your problem

## 🙏 Acknowledgments

- Android Debug Bridge (ADB) - Google/Android Open Source Project
- Electron - Cross-platform desktop app framework
- React - User interface library
- Material-UI - React component library

---

**Made with ❤️ for the Android development community**
