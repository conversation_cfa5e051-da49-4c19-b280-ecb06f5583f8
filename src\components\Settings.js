import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Switch,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Slider,
  Divider,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Grid,
  Card,
  CardContent,
  CardActions,
  Chip,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Settings as SettingsIcon,
  Palette,
  Storage,
  Security,
  Notifications,
  Language,
  Update,
  Backup,
  Restore,
  Delete,
  Download,
  Upload,
  Refresh,
  Save,
  ExpandMore,
  Brightness4,
  Brightness7,
  VolumeUp,
  Speed,
  Folder,
  Info,
  Warning,
  CheckCircle,
  Error
} from '@mui/icons-material';

function Settings({ onThemeChange, onShowNotification }) {
  const [settings, setSettings] = useState({
    theme: 'light',
    language: 'en',
    notifications: true,
    autoUpdate: true,
    transferTimeout: 30,
    maxConcurrentTransfers: 3,
    defaultDownloadPath: '',
    autoBackup: false,
    logLevel: 'info',
    keepLogDays: 7,
    compressionLevel: 5,
    verifyTransfers: true,
    showHiddenFiles: false,
    autoRefreshInterval: 5
  });

  const [showExportDialog, setShowExportDialog] = useState(false);
  const [showImportDialog, setShowImportDialog] = useState(false);
  const [showResetDialog, setShowResetDialog] = useState(false);
  const [exportData, setExportData] = useState('');
  const [importData, setImportData] = useState('');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const savedSettings = await window.electronAPI.settings?.getSettings?.() || {};
      setSettings(prev => ({ ...prev, ...savedSettings }));
    } catch (error) {
      console.error('Failed to load settings:', error);
      onShowNotification('Failed to load settings', 'error');
    }
  };

  const saveSettings = async (newSettings) => {
    try {
      await window.electronAPI.settings?.saveSettings?.(newSettings);
      setSettings(newSettings);
      onShowNotification('Settings saved successfully', 'success');
    } catch (error) {
      console.error('Failed to save settings:', error);
      onShowNotification('Failed to save settings', 'error');
    }
  };

  const handleSettingChange = (key, value) => {
    const newSettings = { ...settings, [key]: value };
    saveSettings(newSettings);

    // Apply theme change immediately
    if (key === 'theme' && onThemeChange) {
      onThemeChange(value);
    }
  };

  const handleExportSettings = () => {
    const exportData = JSON.stringify(settings, null, 2);
    setExportData(exportData);
    setShowExportDialog(true);
  };

  const handleImportSettings = async () => {
    try {
      const importedSettings = JSON.parse(importData);
      await saveSettings(importedSettings);
      setShowImportDialog(false);
      setImportData('');
      onShowNotification('Settings imported successfully', 'success');
    } catch (error) {
      onShowNotification('Invalid settings data', 'error');
    }
  };

  const handleResetSettings = async () => {
    try {
      const defaultSettings = {
        theme: 'light',
        language: 'en',
        notifications: true,
        autoUpdate: true,
        transferTimeout: 30,
        maxConcurrentTransfers: 3,
        defaultDownloadPath: '',
        autoBackup: false,
        logLevel: 'info',
        keepLogDays: 7,
        compressionLevel: 5,
        verifyTransfers: true,
        showHiddenFiles: false,
        autoRefreshInterval: 5
      };

      await saveSettings(defaultSettings);
      setShowResetDialog(false);
      onShowNotification('Settings reset to defaults', 'success');
    } catch (error) {
      onShowNotification('Failed to reset settings', 'error');
    }
  };

  const selectDownloadPath = async () => {
    try {
      const result = await window.electronAPI.dialog?.showOpenDialog?.({
        properties: ['openDirectory']
      });

      if (result && !result.canceled && result.filePaths.length > 0) {
        handleSettingChange('defaultDownloadPath', result.filePaths[0]);
      }
    } catch (error) {
      onShowNotification('Failed to select directory', 'error');
    }
  };

  return (
    <Box sx={{ p: 3, height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h4" component="h1">
          Settings
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            startIcon={<Upload />}
            onClick={handleExportSettings}
          >
            Export
          </Button>
          <Button
            startIcon={<Download />}
            onClick={() => setShowImportDialog(true)}
          >
            Import
          </Button>
          <Button
            startIcon={<Refresh />}
            onClick={() => setShowResetDialog(true)}
            color="warning"
          >
            Reset
          </Button>
        </Box>
      </Box>

      {/* Settings Sections */}
      <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
        {/* Appearance Settings */}
        <Accordion defaultExpanded>
          <AccordionSummary expandIcon={<ExpandMore />}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Palette />
              <Typography variant="h6">Appearance</Typography>
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            <List>
              <ListItem>
                <ListItemIcon>
                  {settings.theme === 'dark' ? <Brightness4 /> : <Brightness7 />}
                </ListItemIcon>
                <ListItemText
                  primary="Theme"
                  secondary="Choose between light and dark theme"
                />
                <ListItemSecondaryAction>
                  <FormControl size="small" sx={{ minWidth: 120 }}>
                    <Select
                      value={settings.theme}
                      onChange={(e) => handleSettingChange('theme', e.target.value)}
                    >
                      <MenuItem value="light">Light</MenuItem>
                      <MenuItem value="dark">Dark</MenuItem>
                      <MenuItem value="auto">Auto</MenuItem>
                    </Select>
                  </FormControl>
                </ListItemSecondaryAction>
              </ListItem>

              <ListItem>
                <ListItemIcon>
                  <Language />
                </ListItemIcon>
                <ListItemText
                  primary="Language"
                  secondary="Select application language"
                />
                <ListItemSecondaryAction>
                  <FormControl size="small" sx={{ minWidth: 120 }}>
                    <Select
                      value={settings.language}
                      onChange={(e) => handleSettingChange('language', e.target.value)}
                    >
                      <MenuItem value="en">English</MenuItem>
                      <MenuItem value="es">Español</MenuItem>
                      <MenuItem value="fr">Français</MenuItem>
                      <MenuItem value="de">Deutsch</MenuItem>
                      <MenuItem value="zh">中文</MenuItem>
                      <MenuItem value="ja">日本語</MenuItem>
                    </Select>
                  </FormControl>
                </ListItemSecondaryAction>
              </ListItem>
            </List>
          </AccordionDetails>
        </Accordion>

        {/* Transfer Settings */}
        <Accordion>
          <AccordionSummary expandIcon={<ExpandMore />}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Speed />
              <Typography variant="h6">Transfer Settings</Typography>
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            <List>
              <ListItem>
                <ListItemIcon>
                  <Storage />
                </ListItemIcon>
                <ListItemText
                  primary="Default Download Path"
                  secondary={settings.defaultDownloadPath || 'Not set'}
                />
                <ListItemSecondaryAction>
                  <Button
                    startIcon={<Folder />}
                    onClick={selectDownloadPath}
                    size="small"
                  >
                    Browse
                  </Button>
                </ListItemSecondaryAction>
              </ListItem>

              <ListItem>
                <ListItemText
                  primary="Transfer Timeout (seconds)"
                  secondary="Maximum time to wait for transfer operations"
                />
                <ListItemSecondaryAction>
                  <Box sx={{ width: 200, mr: 2 }}>
                    <Slider
                      value={settings.transferTimeout}
                      onChange={(e, value) => handleSettingChange('transferTimeout', value)}
                      min={10}
                      max={300}
                      step={10}
                      valueLabelDisplay="auto"
                    />
                  </Box>
                </ListItemSecondaryAction>
              </ListItem>

              <ListItem>
                <ListItemText
                  primary="Max Concurrent Transfers"
                  secondary="Number of simultaneous file transfers"
                />
                <ListItemSecondaryAction>
                  <Box sx={{ width: 200, mr: 2 }}>
                    <Slider
                      value={settings.maxConcurrentTransfers}
                      onChange={(e, value) => handleSettingChange('maxConcurrentTransfers', value)}
                      min={1}
                      max={10}
                      step={1}
                      valueLabelDisplay="auto"
                    />
                  </Box>
                </ListItemSecondaryAction>
              </ListItem>

              <ListItem>
                <ListItemIcon>
                  <CheckCircle />
                </ListItemIcon>
                <ListItemText
                  primary="Verify Transfers"
                  secondary="Check file integrity after transfer"
                />
                <ListItemSecondaryAction>
                  <Switch
                    checked={settings.verifyTransfers}
                    onChange={(e) => handleSettingChange('verifyTransfers', e.target.checked)}
                  />
                </ListItemSecondaryAction>
              </ListItem>
            </List>
          </AccordionDetails>
        </Accordion>

        {/* Application Settings */}
        <Accordion>
          <AccordionSummary expandIcon={<ExpandMore />}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <SettingsIcon />
              <Typography variant="h6">Application</Typography>
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            <List>
              <ListItem>
                <ListItemIcon>
                  <Notifications />
                </ListItemIcon>
                <ListItemText
                  primary="Notifications"
                  secondary="Show desktop notifications"
                />
                <ListItemSecondaryAction>
                  <Switch
                    checked={settings.notifications}
                    onChange={(e) => handleSettingChange('notifications', e.target.checked)}
                  />
                </ListItemSecondaryAction>
              </ListItem>

              <ListItem>
                <ListItemIcon>
                  <Update />
                </ListItemIcon>
                <ListItemText
                  primary="Auto Update"
                  secondary="Automatically check for updates"
                />
                <ListItemSecondaryAction>
                  <Switch
                    checked={settings.autoUpdate}
                    onChange={(e) => handleSettingChange('autoUpdate', e.target.checked)}
                  />
                </ListItemSecondaryAction>
              </ListItem>

              <ListItem>
                <ListItemText
                  primary="Auto Refresh Interval (seconds)"
                  secondary="How often to refresh device information"
                />
                <ListItemSecondaryAction>
                  <Box sx={{ width: 200, mr: 2 }}>
                    <Slider
                      value={settings.autoRefreshInterval}
                      onChange={(e, value) => handleSettingChange('autoRefreshInterval', value)}
                      min={1}
                      max={60}
                      step={1}
                      valueLabelDisplay="auto"
                    />
                  </Box>
                </ListItemSecondaryAction>
              </ListItem>

              <ListItem>
                <ListItemIcon>
                  <Security />
                </ListItemIcon>
                <ListItemText
                  primary="Show Hidden Files"
                  secondary="Display hidden files in file browser"
                />
                <ListItemSecondaryAction>
                  <Switch
                    checked={settings.showHiddenFiles}
                    onChange={(e) => handleSettingChange('showHiddenFiles', e.target.checked)}
                  />
                </ListItemSecondaryAction>
              </ListItem>
            </List>
          </AccordionDetails>
        </Accordion>

        {/* Advanced Settings */}
        <Accordion>
          <AccordionSummary expandIcon={<ExpandMore />}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Warning />
              <Typography variant="h6">Advanced</Typography>
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            <List>
              <ListItem>
                <ListItemIcon>
                  <Info />
                </ListItemIcon>
                <ListItemText
                  primary="Log Level"
                  secondary="Application logging verbosity"
                />
                <ListItemSecondaryAction>
                  <FormControl size="small" sx={{ minWidth: 120 }}>
                    <Select
                      value={settings.logLevel}
                      onChange={(e) => handleSettingChange('logLevel', e.target.value)}
                    >
                      <MenuItem value="error">Error</MenuItem>
                      <MenuItem value="warn">Warning</MenuItem>
                      <MenuItem value="info">Info</MenuItem>
                      <MenuItem value="debug">Debug</MenuItem>
                    </Select>
                  </FormControl>
                </ListItemSecondaryAction>
              </ListItem>

              <ListItem>
                <ListItemText
                  primary="Keep Log Files (days)"
                  secondary="How long to keep application logs"
                />
                <ListItemSecondaryAction>
                  <Box sx={{ width: 200, mr: 2 }}>
                    <Slider
                      value={settings.keepLogDays}
                      onChange={(e, value) => handleSettingChange('keepLogDays', value)}
                      min={1}
                      max={30}
                      step={1}
                      valueLabelDisplay="auto"
                    />
                  </Box>
                </ListItemSecondaryAction>
              </ListItem>

              <ListItem>
                <ListItemIcon>
                  <Backup />
                </ListItemIcon>
                <ListItemText
                  primary="Auto Backup Settings"
                  secondary="Automatically backup settings"
                />
                <ListItemSecondaryAction>
                  <Switch
                    checked={settings.autoBackup}
                    onChange={(e) => handleSettingChange('autoBackup', e.target.checked)}
                  />
                </ListItemSecondaryAction>
              </ListItem>
            </List>
          </AccordionDetails>
        </Accordion>
      </Box>

      {/* Export Settings Dialog */}
      <Dialog open={showExportDialog} onClose={() => setShowExportDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Export Settings</DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            multiline
            rows={10}
            value={exportData}
            variant="outlined"
            InputProps={{
              readOnly: true,
            }}
            sx={{ mt: 1 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowExportDialog(false)}>Close</Button>
          <Button
            onClick={() => {
              navigator.clipboard.writeText(exportData);
              onShowNotification('Settings copied to clipboard', 'success');
            }}
            variant="contained"
          >
            Copy to Clipboard
          </Button>
        </DialogActions>
      </Dialog>

      {/* Import Settings Dialog */}
      <Dialog open={showImportDialog} onClose={() => setShowImportDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Import Settings</DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            multiline
            rows={10}
            value={importData}
            onChange={(e) => setImportData(e.target.value)}
            placeholder="Paste settings JSON here..."
            variant="outlined"
            sx={{ mt: 1 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowImportDialog(false)}>Cancel</Button>
          <Button
            onClick={handleImportSettings}
            variant="contained"
            disabled={!importData.trim()}
          >
            Import
          </Button>
        </DialogActions>
      </Dialog>

      {/* Reset Settings Dialog */}
      <Dialog open={showResetDialog} onClose={() => setShowResetDialog(false)}>
        <DialogTitle>Reset Settings</DialogTitle>
        <DialogContent>
          <Alert severity="warning" sx={{ mb: 2 }}>
            This will reset all settings to their default values. This action cannot be undone.
          </Alert>
          <Typography variant="body2">
            Are you sure you want to reset all settings to defaults?
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowResetDialog(false)}>Cancel</Button>
          <Button
            onClick={handleResetSettings}
            variant="contained"
            color="warning"
          >
            Reset
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default Settings;
