const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

console.log('Preload script starting...');

// Expose protected methods that allow the renderer process to use
// the ipc<PERSON>enderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // Device management
  device: {
    list: () => ipcRenderer.invoke('device:list'),
    connect: (deviceId) => ipcRenderer.invoke('device:connect', deviceId),
    disconnect: (deviceId) => ipcRenderer.invoke('device:disconnect', deviceId),
    getInfo: (deviceId) => ipcRenderer.invoke('device:info', deviceId),
    getDetailedInfo: (deviceId) => ipcRenderer.invoke('device:getDetailedInfo', deviceId),
    getAdbStatus: () => ipcRenderer.invoke('device:getAdbStatus'),
    restartAdb: () => ipcRenderer.invoke('device:restartAdb'),
    takeScreenshot: (deviceId) => ipcRenderer.invoke('device:takeScreenshot', deviceId),
    saveScreenshot: (imagePath) => ipcRenderer.invoke('device:saveScreenshot', imagePath),
    startLogcat: (deviceId, options) => ipcRenderer.invoke('device:startLogcat', deviceId, options),
    stopLogcat: (deviceId) => ipcRenderer.invoke('device:stopLogcat', deviceId),
    saveLogcat: (logs) => ipcRenderer.invoke('device:saveLogcat', logs),
    getLogcat: (deviceId, options) => ipcRenderer.invoke('device:logcat', deviceId, options),
    executeShell: (deviceId, command) => ipcRenderer.invoke('device:executeShell', deviceId, command),
    reboot: (deviceId, mode) => ipcRenderer.invoke('device:reboot', deviceId, mode),
    toggleWifi: (deviceId) => ipcRenderer.invoke('device:toggleWifi', deviceId),

    // Event listeners
    onLogcatData: (callback) => {
      ipcRenderer.on('logcat-data', (event, data) => callback(data));
      return () => ipcRenderer.removeAllListeners('logcat-data');
    }
  },

  // File operations
  file: {
    list: (deviceId, path) => ipcRenderer.invoke('file:list', deviceId, path),
    listPC: (path) => ipcRenderer.invoke('file:list-pc', path),
    createFolder: (deviceId, path, name) => ipcRenderer.invoke('file:create-folder', deviceId, path, name),
    createFolderPC: (dirPath, folderName) => ipcRenderer.invoke('file:createFolderPC', dirPath, folderName),
    delete: (deviceId, paths) => ipcRenderer.invoke('file:delete', deviceId, paths),
    deletePC: (filePaths) => ipcRenderer.invoke('file:deletePC', filePaths),
    rename: (deviceId, oldPath, newPath) => ipcRenderer.invoke('file:rename', deviceId, oldPath, newPath),
    renamePC: (oldPath, newPath) => ipcRenderer.invoke('file:renamePC', oldPath, newPath),
    copyPC: (sourcePaths, destinationDir) => ipcRenderer.invoke('file:copyPC', sourcePaths, destinationDir),
    getInfo: (filePath) => ipcRenderer.invoke('file:getInfo', filePath),
    search: (searchDir, query, options) => ipcRenderer.invoke('file:search', searchDir, query, options),
    getDrives: () => ipcRenderer.invoke('file:getDrives')
  },

  // Transfer operations
  transfer: {
    upload: (deviceId, localPaths, remotePath) => ipcRenderer.invoke('transfer:upload', deviceId, localPaths, remotePath),
    download: (deviceId, remotePaths, localPath) => ipcRenderer.invoke('transfer:download', deviceId, remotePaths, localPath),
    getQueue: () => ipcRenderer.invoke('transfer:queue'),
    cancel: (transferId) => ipcRenderer.invoke('transfer:cancel', transferId),
    pause: (transferId) => ipcRenderer.invoke('transfer:pause', transferId),
    resume: (transferId) => ipcRenderer.invoke('transfer:resume', transferId),
    retry: (transferId) => ipcRenderer.invoke('transfer:retry', transferId),
    getHistory: () => ipcRenderer.invoke('transfer:history'),
    clearHistory: () => ipcRenderer.invoke('transfer:clearHistory'),
    getStats: () => ipcRenderer.invoke('transfer:getStats'),

    // Event listeners for transfer progress
    onProgress: (callback) => {
      ipcRenderer.on('transfer:progress', (event, data) => callback(data));
      return () => ipcRenderer.removeAllListeners('transfer:progress');
    },
    onComplete: (callback) => {
      ipcRenderer.on('transfer:complete', (event, data) => callback(data));
      return () => ipcRenderer.removeAllListeners('transfer:complete');
    },
    onError: (callback) => {
      ipcRenderer.on('transfer:error', (event, data) => callback(data));
      return () => ipcRenderer.removeAllListeners('transfer:error');
    }
  },

  // Application management
  app: {
    list: (deviceId) => ipcRenderer.invoke('app:list', deviceId),
    install: (deviceId, apkPath) => ipcRenderer.invoke('app:install', deviceId, apkPath),
    uninstall: (deviceId, packageName) => ipcRenderer.invoke('app:uninstall', deviceId, packageName),
    backup: (deviceId, packageName, outputPath) => ipcRenderer.invoke('app:backup', deviceId, packageName, outputPath),
    launch: (deviceId, packageName) => ipcRenderer.invoke('app:launch', deviceId, packageName)
  },

  // System dialogs
  dialog: {
    openFile: (options) => ipcRenderer.invoke('dialog:open-file', options),
    saveFile: (options) => ipcRenderer.invoke('dialog:save-file', options)
  },

  // Settings
  settings: {
    get: () => ipcRenderer.invoke('settings:get'),
    set: (settings) => ipcRenderer.invoke('settings:set', settings),
    reset: () => ipcRenderer.invoke('settings:reset'),
    export: () => ipcRenderer.invoke('settings:export'),
    import: (settingsData) => ipcRenderer.invoke('settings:import', settingsData),
    getSetting: (key) => ipcRenderer.invoke('settings:getSetting', key),
    setSetting: (key, value) => ipcRenderer.invoke('settings:setSetting', key, value),
    validate: (settings) => ipcRenderer.invoke('settings:validate', settings),
    backup: () => ipcRenderer.invoke('settings:backup'),
    restore: (backupPath) => ipcRenderer.invoke('settings:restore', backupPath),
    getInfo: () => ipcRenderer.invoke('settings:getInfo')
  },

  // Event listeners for device changes
  onDeviceConnected: (callback) => {
    ipcRenderer.on('device:connected', (event, device) => callback(device));
    return () => ipcRenderer.removeAllListeners('device:connected');
  },
  
  onDeviceDisconnected: (callback) => {
    ipcRenderer.on('device:disconnected', (event, deviceId) => callback(deviceId));
    return () => ipcRenderer.removeAllListeners('device:disconnected');
  },

  // Utility functions
  utils: {
    openExternal: (url) => ipcRenderer.invoke('utils:open-external', url),
    showNotification: (title, body) => ipcRenderer.invoke('utils:show-notification', title, body)
  }
});

console.log('Preload script completed. electronAPI exposed to window.');
