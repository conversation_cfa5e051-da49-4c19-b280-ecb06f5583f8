import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Box,
  Typo<PERSON>,
  <PERSON>lapse,
  IconButton,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  Stack
} from '@mui/material';
import {
  Error as ErrorIcon,
  Warning as WarningIcon,
  Refresh as RefreshIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Usb as UsbIcon,
  Settings as SettingsIcon,
  PhoneAndroid as PhoneIcon,
  Computer as ComputerIcon
} from '@mui/icons-material';
import { checkDeviceConnection } from '../utils/errorHandler';

const DeviceConnectionAlert = ({ 
  selectedDevice, 
  onRetry, 
  onDeviceSelect,
  error,
  showTroubleshooting = true 
}) => {
  const [expanded, setExpanded] = useState(false);
  const [checking, setChecking] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState(null);

  useEffect(() => {
    if (selectedDevice) {
      checkConnection();
    }
  }, [selectedDevice]);

  const checkConnection = async () => {
    if (!selectedDevice) return;

    try {
      setChecking(true);
      const status = await checkDeviceConnection(selectedDevice.id);
      setConnectionStatus(status);
    } catch (error) {
      setConnectionStatus({ connected: false, error: error.message });
    } finally {
      setChecking(false);
    }
  };

  const handleRetry = async () => {
    await checkConnection();
    if (onRetry) {
      onRetry();
    }
  };

  const troubleshootingSteps = [
    {
      icon: <UsbIcon />,
      title: 'Check USB Connection',
      description: 'Ensure the USB cable is properly connected and working'
    },
    {
      icon: <SettingsIcon />,
      title: 'Enable USB Debugging',
      description: 'Go to Settings > Developer Options > USB Debugging'
    },
    {
      icon: <PhoneIcon />,
      title: 'Authorize Computer',
      description: 'Accept the USB debugging authorization dialog on your device'
    },
    {
      icon: <ComputerIcon />,
      title: 'Restart ADB',
      description: 'Try restarting the ADB server or reconnecting the device'
    }
  ];

  if (!selectedDevice) {
    return (
      <Alert severity="info" sx={{ mb: 2 }}>
        <AlertTitle>No Device Selected</AlertTitle>
        Please connect and select an Android device to continue.
        {onDeviceSelect && (
          <Button 
            variant="outlined" 
            size="small" 
            onClick={onDeviceSelect}
            sx={{ mt: 1 }}
          >
            Select Device
          </Button>
        )}
      </Alert>
    );
  }

  if (connectionStatus?.connected && !error) {
    return null; // No alert needed when connected
  }

  const severity = error || !connectionStatus?.connected ? 'error' : 'warning';
  const title = error ? 'Operation Failed' : 'Device Connection Issue';
  const message = error || connectionStatus?.error || 'Unable to communicate with device';

  return (
    <Alert 
      severity={severity} 
      sx={{ mb: 2 }}
      action={
        <Stack direction="row" spacing={1}>
          <Button
            color="inherit"
            size="small"
            onClick={handleRetry}
            disabled={checking}
            startIcon={<RefreshIcon />}
          >
            {checking ? 'Checking...' : 'Retry'}
          </Button>
          {showTroubleshooting && (
            <IconButton
              color="inherit"
              size="small"
              onClick={() => setExpanded(!expanded)}
            >
              {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            </IconButton>
          )}
        </Stack>
      }
    >
      <AlertTitle>{title}</AlertTitle>
      
      <Box sx={{ mb: 1 }}>
        <Typography variant="body2">
          {message}
        </Typography>
        
        {selectedDevice && (
          <Box sx={{ mt: 1 }}>
            <Chip 
              label={`Device: ${selectedDevice.name || selectedDevice.id}`}
              size="small"
              variant="outlined"
            />
          </Box>
        )}
      </Box>

      {showTroubleshooting && (
        <Collapse in={expanded}>
          <Box sx={{ mt: 2 }}>
            <Typography variant="subtitle2" gutterBottom>
              Troubleshooting Steps:
            </Typography>
            <List dense>
              {troubleshootingSteps.map((step, index) => (
                <ListItem key={index} sx={{ py: 0.5 }}>
                  <ListItemIcon sx={{ minWidth: 36 }}>
                    {step.icon}
                  </ListItemIcon>
                  <ListItemText
                    primary={step.title}
                    secondary={step.description}
                    primaryTypographyProps={{ variant: 'body2', fontWeight: 'medium' }}
                    secondaryTypographyProps={{ variant: 'caption' }}
                  />
                </ListItem>
              ))}
            </List>
          </Box>
        </Collapse>
      )}
    </Alert>
  );
};

export default DeviceConnectionAlert;
