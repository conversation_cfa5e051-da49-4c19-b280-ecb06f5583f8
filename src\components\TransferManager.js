import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  LinearProgress,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tabs,
  Tab,
  Card,
  CardContent,
  Grid,
  Divider,
  Alert,
  Tooltip,
  Menu,
  MenuItem,
  CircularProgress,
  Stack
} from '@mui/material';
import {
  validateInput,
  createRetryHandler,
  checkDeviceConnection,
  logError,
  getErrorMessage
} from '../utils/errorHandler';
import { useNotifications } from './NotificationSystem';
import {
  SwapHoriz,
  Upload,
  Download,
  Pause,
  PlayArrow,
  Stop,
  Delete,
  Refresh,
  History,
  Queue as QueueIcon,
  CheckCircle,
  Error,
  Warning,
  Info,
  MoreVert,
  Clear,
  Speed
} from '@mui/icons-material';

function TransferManager({ onShowNotification }) {
  const [activeTransfers, setActiveTransfers] = useState([]);
  const [transferHistory, setTransferHistory] = useState([]);
  const [transferQueue, setTransferQueue] = useState([]);
  const [currentTab, setCurrentTab] = useState(0);
  const [selectedTransfer, setSelectedTransfer] = useState(null);
  const [showDetails, setShowDetails] = useState(false);
  const [contextMenu, setContextMenu] = useState(null);
  const [contextTransfer, setContextTransfer] = useState(null);
  const [loading, setLoading] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [connectionErrors, setConnectionErrors] = useState(new Map());

  // Use enhanced notification system
  const { showError, showSuccess, showWarning, showInfo } = useNotifications();

  useEffect(() => {
    loadTransfers();

    // Set up real-time transfer updates
    const handleTransferUpdate = (data) => {
      updateTransferInList(data);
    };

    const handleTransferComplete = (data) => {
      moveToHistory(data);
      onShowNotification(`Transfer completed: ${data.files?.length || 0} files`, 'success');
    };

    const handleTransferError = (data) => {
      updateTransferInList({ ...data, status: 'failed' });
      onShowNotification(`Transfer failed: ${data.error}`, 'error');
    };

    // Add event listeners if they exist
    if (window.electronAPI?.transfer) {
      window.electronAPI.transfer.onProgress?.(handleTransferUpdate);
      window.electronAPI.transfer.onComplete?.(handleTransferComplete);
      window.electronAPI.transfer.onError?.(handleTransferError);
    }

    return () => {
      // Cleanup listeners if needed
    };
  }, [onShowNotification]);

  const loadTransfers = createRetryHandler(async () => {
    try {
      setLoading(true);
      setConnectionErrors(new Map());

      // Check if transfer API is available
      if (!window.electronAPI?.transfer) {
        throw new Error('Transfer API not available');
      }

      const [active, history, queue] = await Promise.all([
        window.electronAPI.transfer.getActive?.() || [],
        window.electronAPI.transfer.getHistory?.() || [],
        window.electronAPI.transfer.getQueue?.() || []
      ]);

      setActiveTransfers(active);
      setTransferHistory(history);
      setTransferQueue(queue);
      setRetryCount(0); // Reset retry count on success

      if (retryCount > 0) {
        showSuccess('Transfer data loaded successfully');
      }
    } catch (error) {
      logError(error, 'Loading transfer data');

      showError(error, 'Loading transfer data', {
        retryAction: () => loadTransfers(),
        suggestions: [
          'Check if the application is properly initialized',
          'Try refreshing the page',
          'Restart the application if the issue persists',
          'Check your system permissions'
        ]
      });

      setRetryCount(prev => prev + 1);
      throw error; // Re-throw for retry handler
    } finally {
      setLoading(false);
    }
  }, 3, 2000);

  const updateTransferInList = (updatedTransfer) => {
    setActiveTransfers(prev =>
      prev.map(transfer =>
        transfer.id === updatedTransfer.id ? { ...transfer, ...updatedTransfer } : transfer
      )
    );
  };

  const moveToHistory = (completedTransfer) => {
    setActiveTransfers(prev => prev.filter(t => t.id !== completedTransfer.id));
    setTransferHistory(prev => [completedTransfer, ...prev]);
  };

  const handlePauseResume = async (transferId, action) => {
    try {
      // Validate inputs
      validateInput.required(transferId, 'Transfer ID');
      validateInput.required(action, 'Action');

      if (!['pause', 'resume'].includes(action)) {
        throw new Error('Invalid action. Must be "pause" or "resume"');
      }

      if (action === 'pause') {
        await window.electronAPI.transfer.pause(transferId);
        showInfo('Transfer paused successfully');
      } else {
        await window.electronAPI.transfer.resume(transferId);
        showInfo('Transfer resumed successfully');
      }

      // Refresh transfer data
      await loadTransfers();
    } catch (error) {
      logError(error, `${action} transfer`, { transferId });

      showError(error, `${action} transfer`, {
        retryAction: () => handlePauseResume(transferId, action),
        suggestions: [
          'Check if the transfer is still active',
          'Verify the transfer ID is valid',
          'Try refreshing the transfer list',
          'Restart the transfer if needed'
        ]
      });
    }
  };

  const handleCancel = async (transferId) => {
    try {
      validateInput.required(transferId, 'Transfer ID');

      if (!window.confirm('Are you sure you want to cancel this transfer?\n\nThis action cannot be undone.')) {
        return;
      }

      await window.electronAPI.transfer.cancel(transferId);
      setActiveTransfers(prev => prev.filter(t => t.id !== transferId));
      showInfo('Transfer cancelled successfully');
    } catch (error) {
      logError(error, 'Cancel transfer', { transferId });

      showError(error, 'Cancel transfer', {
        retryAction: () => handleCancel(transferId),
        suggestions: [
          'Check if the transfer is still active',
          'Try refreshing the transfer list',
          'Force close the transfer if needed',
          'Restart the application if the issue persists'
        ]
      });
    }
  };

  const handleRetry = async (transferId) => {
    try {
      validateInput.required(transferId, 'Transfer ID');

      await window.electronAPI.transfer.retry(transferId);
      showInfo('Transfer restarted successfully');

      // Refresh transfer data
      await loadTransfers();
    } catch (error) {
      logError(error, 'Retry transfer', { transferId });

      showError(error, 'Retry transfer', {
        retryAction: () => handleRetry(transferId),
        suggestions: [
          'Check device connection',
          'Verify source and destination paths',
          'Ensure sufficient storage space',
          'Try starting a new transfer instead'
        ]
      });
    }
  };

  const handleClearHistory = async () => {
    if (!window.confirm('Are you sure you want to clear transfer history?')) {
      return;
    }

    try {
      await window.electronAPI.transfer.clearHistory();
      setTransferHistory([]);
      onShowNotification('Transfer history cleared', 'info');
    } catch (error) {
      onShowNotification('Failed to clear history', 'error');
    }
  };

  const handleContextMenu = (event, transfer) => {
    event.preventDefault();
    setContextMenu({
      mouseX: event.clientX - 2,
      mouseY: event.clientY - 4,
    });
    setContextTransfer(transfer);
  };

  const closeContextMenu = () => {
    setContextMenu(null);
    setContextTransfer(null);
  };

  const getTransferIcon = (transfer) => {
    switch (transfer.type) {
      case 'upload':
        return <Upload color="primary" />;
      case 'download':
        return <Download color="secondary" />;
      default:
        return <SwapHoriz />;
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return <CheckCircle color="success" />;
      case 'failed':
        return <Error color="error" />;
      case 'paused':
        return <Pause color="warning" />;
      case 'active':
        return <Speed color="info" />;
      case 'queued':
        return <QueueIcon color="action" />;
      default:
        return <Info />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'failed':
        return 'error';
      case 'paused':
        return 'warning';
      case 'active':
        return 'info';
      case 'queued':
        return 'default';
      default:
        return 'default';
    }
  };

  const formatFileSize = (bytes) => {
    if (!bytes) return '0 B';
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
  };

  const formatDuration = (ms) => {
    if (!ms) return '';
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  };

  const calculateSpeed = (transferred, startTime) => {
    if (!transferred || !startTime) return '';
    const elapsed = Date.now() - new Date(startTime).getTime();
    const speed = transferred / (elapsed / 1000);
    return `${formatFileSize(speed)}/s`;
  };

  const renderTransferItem = (transfer, showActions = true) => (
    <ListItem
      key={transfer.id}
      onContextMenu={(e) => handleContextMenu(e, transfer)}
      sx={{
        border: 1,
        borderColor: 'divider',
        borderRadius: 1,
        mb: 1,
        '&:hover': {
          backgroundColor: 'action.hover',
        },
      }}
    >
      <ListItemIcon>
        {getTransferIcon(transfer)}
      </ListItemIcon>
      <ListItemText
        primary={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Typography variant="subtitle2" noWrap>
              {transfer.files?.[0]?.name || 'Multiple files'}
            </Typography>
            <Chip
              icon={getStatusIcon(transfer.status)}
              label={transfer.status}
              size="small"
              color={getStatusColor(transfer.status)}
              variant="outlined"
            />
          </Box>
        }
        secondary={
          <Box>
            <Typography variant="caption" color="text.secondary">
              {transfer.type === 'upload' ? 'To device' : 'To PC'} • {transfer.files?.length || 0} files • {formatFileSize(transfer.totalSize)}
            </Typography>
            {transfer.status === 'active' && (
              <Box sx={{ mt: 1 }}>
                <LinearProgress
                  variant="determinate"
                  value={transfer.progress || 0}
                  sx={{ mb: 0.5 }}
                />
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Typography variant="caption" color="text.secondary">
                    {Math.round(transfer.progress || 0)}% • {calculateSpeed(transfer.totalTransferred, transfer.startedAt)}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {formatFileSize(transfer.totalTransferred)} / {formatFileSize(transfer.totalSize)}
                  </Typography>
                </Box>
              </Box>
            )}
            {transfer.error && (
              <Alert severity="error" sx={{ mt: 1 }}>
                {transfer.error}
              </Alert>
            )}
          </Box>
        }
      />
      {showActions && (
        <ListItemSecondaryAction>
          <Box sx={{ display: 'flex', gap: 0.5 }}>
            {transfer.status === 'active' && (
              <Tooltip title="Pause">
                <IconButton
                  size="small"
                  onClick={() => handlePauseResume(transfer.id, 'pause')}
                >
                  <Pause />
                </IconButton>
              </Tooltip>
            )}
            {transfer.status === 'paused' && (
              <Tooltip title="Resume">
                <IconButton
                  size="small"
                  onClick={() => handlePauseResume(transfer.id, 'resume')}
                >
                  <PlayArrow />
                </IconButton>
              </Tooltip>
            )}
            {transfer.status === 'failed' && (
              <Tooltip title="Retry">
                <IconButton
                  size="small"
                  onClick={() => handleRetry(transfer.id)}
                >
                  <Refresh />
                </IconButton>
              </Tooltip>
            )}
            {(transfer.status === 'active' || transfer.status === 'paused' || transfer.status === 'queued') && (
              <Tooltip title="Cancel">
                <IconButton
                  size="small"
                  onClick={() => handleCancel(transfer.id)}
                  color="error"
                >
                  <Stop />
                </IconButton>
              </Tooltip>
            )}
            <Tooltip title="Details">
              <IconButton
                size="small"
                onClick={() => {
                  setSelectedTransfer(transfer);
                  setShowDetails(true);
                }}
              >
                <Info />
              </IconButton>
            </Tooltip>
          </Box>
        </ListItemSecondaryAction>
      )}
    </ListItem>
  );

  return (
    <Box sx={{ p: 3, height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h4" component="h1">
          Transfer Manager
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            startIcon={<Refresh />}
            onClick={loadTransfers}
            disabled={loading}
          >
            Refresh
          </Button>
          {currentTab === 1 && transferHistory.length > 0 && (
            <Button
              startIcon={<Clear />}
              onClick={handleClearHistory}
              color="error"
            >
              Clear History
            </Button>
          )}
        </Box>
      </Box>

      {/* Statistics Cards */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="primary">
                {activeTransfers.length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Active Transfers
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="secondary">
                {transferQueue.length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Queued Transfers
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="success.main">
                {transferHistory.filter(t => t.status === 'completed').length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Completed Today
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Tabs */}
      <Paper sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
        <Tabs
          value={currentTab}
          onChange={(e, newValue) => setCurrentTab(newValue)}
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          <Tab
            icon={<Speed />}
            label={`Active (${activeTransfers.length})`}
            iconPosition="start"
          />
          <Tab
            icon={<History />}
            label={`History (${transferHistory.length})`}
            iconPosition="start"
          />
          <Tab
            icon={<QueueIcon />}
            label={`Queue (${transferQueue.length})`}
            iconPosition="start"
          />
        </Tabs>

        {/* Tab Content */}
        <Box sx={{ flexGrow: 1, overflow: 'auto', p: 2 }}>
          {currentTab === 0 && (
            <Box>
              {activeTransfers.length === 0 ? (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <Speed sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                  <Typography variant="h6" gutterBottom>
                    No Active Transfers
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Start transferring files from the File Browser to see them here.
                  </Typography>
                </Box>
              ) : (
                <List>
                  {activeTransfers.map(transfer => renderTransferItem(transfer, true))}
                </List>
              )}
            </Box>
          )}

          {currentTab === 1 && (
            <Box>
              {transferHistory.length === 0 ? (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <History sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                  <Typography variant="h6" gutterBottom>
                    No Transfer History
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Completed transfers will appear here.
                  </Typography>
                </Box>
              ) : (
                <List>
                  {transferHistory.map(transfer => renderTransferItem(transfer, false))}
                </List>
              )}
            </Box>
          )}

          {currentTab === 2 && (
            <Box>
              {transferQueue.length === 0 ? (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <QueueIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                  <Typography variant="h6" gutterBottom>
                    No Queued Transfers
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Transfers waiting to start will appear here.
                  </Typography>
                </Box>
              ) : (
                <List>
                  {transferQueue.map(transfer => renderTransferItem(transfer, true))}
                </List>
              )}
            </Box>
          )}
        </Box>
      </Paper>

      {/* Context Menu */}
      <Menu
        open={contextMenu !== null}
        onClose={closeContextMenu}
        anchorReference="anchorPosition"
        anchorPosition={
          contextMenu !== null
            ? { top: contextMenu.mouseY, left: contextMenu.mouseX }
            : undefined
        }
      >
        {contextTransfer && (
          <>
            {contextTransfer.status === 'active' && (
              <MenuItem onClick={() => { handlePauseResume(contextTransfer.id, 'pause'); closeContextMenu(); }}>
                <ListItemIcon><Pause fontSize="small" /></ListItemIcon>
                <ListItemText>Pause</ListItemText>
              </MenuItem>
            )}
            {contextTransfer.status === 'paused' && (
              <MenuItem onClick={() => { handlePauseResume(contextTransfer.id, 'resume'); closeContextMenu(); }}>
                <ListItemIcon><PlayArrow fontSize="small" /></ListItemIcon>
                <ListItemText>Resume</ListItemText>
              </MenuItem>
            )}
            {contextTransfer.status === 'failed' && (
              <MenuItem onClick={() => { handleRetry(contextTransfer.id); closeContextMenu(); }}>
                <ListItemIcon><Refresh fontSize="small" /></ListItemIcon>
                <ListItemText>Retry</ListItemText>
              </MenuItem>
            )}
            <MenuItem onClick={() => { setSelectedTransfer(contextTransfer); setShowDetails(true); closeContextMenu(); }}>
              <ListItemIcon><Info fontSize="small" /></ListItemIcon>
              <ListItemText>Details</ListItemText>
            </MenuItem>
            <Divider />
            <MenuItem onClick={() => { handleCancel(contextTransfer.id); closeContextMenu(); }} sx={{ color: 'error.main' }}>
              <ListItemIcon><Delete fontSize="small" color="error" /></ListItemIcon>
              <ListItemText>Cancel</ListItemText>
            </MenuItem>
          </>
        )}
      </Menu>

      {/* Transfer Details Dialog */}
      <Dialog open={showDetails} onClose={() => setShowDetails(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          Transfer Details
          {selectedTransfer && (
            <Chip
              icon={getStatusIcon(selectedTransfer.status)}
              label={selectedTransfer.status}
              size="small"
              color={getStatusColor(selectedTransfer.status)}
              sx={{ ml: 2 }}
            />
          )}
        </DialogTitle>
        <DialogContent>
          {selectedTransfer && (
            <Box>
              <Grid container spacing={2} sx={{ mb: 3 }}>
                <Grid item xs={6}>
                  <Typography variant="subtitle2" gutterBottom>Transfer Type</Typography>
                  <Typography variant="body2" color="text.secondary">
                    {selectedTransfer.type === 'upload' ? 'Upload to Device' : 'Download to PC'}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="subtitle2" gutterBottom>Total Size</Typography>
                  <Typography variant="body2" color="text.secondary">
                    {formatFileSize(selectedTransfer.totalSize)}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="subtitle2" gutterBottom>Progress</Typography>
                  <Typography variant="body2" color="text.secondary">
                    {Math.round(selectedTransfer.progress || 0)}%
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="subtitle2" gutterBottom>Speed</Typography>
                  <Typography variant="body2" color="text.secondary">
                    {calculateSpeed(selectedTransfer.totalTransferred, selectedTransfer.startedAt)}
                  </Typography>
                </Grid>
                {selectedTransfer.startedAt && (
                  <Grid item xs={6}>
                    <Typography variant="subtitle2" gutterBottom>Started</Typography>
                    <Typography variant="body2" color="text.secondary">
                      {new Date(selectedTransfer.startedAt).toLocaleString()}
                    </Typography>
                  </Grid>
                )}
                {selectedTransfer.completedAt && (
                  <Grid item xs={6}>
                    <Typography variant="subtitle2" gutterBottom>Completed</Typography>
                    <Typography variant="body2" color="text.secondary">
                      {new Date(selectedTransfer.completedAt).toLocaleString()}
                    </Typography>
                  </Grid>
                )}
              </Grid>

              <Typography variant="subtitle2" gutterBottom>Files</Typography>
              <List dense sx={{ maxHeight: 300, overflow: 'auto' }}>
                {selectedTransfer.files?.map((file, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>
                      {getStatusIcon(file.status)}
                    </ListItemIcon>
                    <ListItemText
                      primary={file.name || file.localPath?.split('/').pop() || file.remotePath?.split('/').pop()}
                      secondary={
                        <Box>
                          <Typography variant="caption" color="text.secondary">
                            {formatFileSize(file.size)} • {file.status}
                          </Typography>
                          {file.status === 'transferring' && (
                            <LinearProgress
                              variant="determinate"
                              value={file.progress || 0}
                              sx={{ mt: 0.5 }}
                            />
                          )}
                          {file.error && (
                            <Typography variant="caption" color="error">
                              Error: {file.error}
                            </Typography>
                          )}
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowDetails(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default TransferManager;
