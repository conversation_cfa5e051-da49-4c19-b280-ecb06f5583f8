const path = require('path');
const fs = require('fs-extra');
const os = require('os');

// Error types for better error handling
const ErrorTypes = {
  DEVICE_NOT_FOUND: 'DEVICE_NOT_FOUND',
  DEVICE_UNAUTHORIZED: 'DEVICE_UNAUTHORIZED',
  DEVICE_OFFLINE: 'DEVICE_OFFLINE',
  ADB_ERROR: 'ADB_ERROR',
  PERMISSION_DENIED: 'PERMISSION_DENIED',
  TIMEOUT: 'TIMEOUT',
  INVALID_INPUT: 'INVALID_INPUT'
};

class DeviceError extends Error {
  constructor(message, type = ErrorTypes.ADB_ERROR, deviceId = null) {
    super(message);
    this.name = 'DeviceError';
    this.type = type;
    this.deviceId = deviceId;
  }
}

class DeviceService {
  constructor(adbService, storageService) {
    this.adb = adbService;
    this.storage = storageService;
    this.connectedDevices = new Map();
    this.deviceTimeouts = new Map();
  }

  // Input validation helpers
  validateDeviceId(deviceId) {
    if (!deviceId || typeof deviceId !== 'string') {
      throw new DeviceError('Invalid device ID provided', ErrorTypes.INVALID_INPUT);
    }
  }

  validatePath(filePath) {
    if (!filePath || typeof filePath !== 'string') {
      throw new DeviceError('Invalid path provided', ErrorTypes.INVALID_INPUT);
    }
  }

  // Device connection checker
  async isDeviceConnected(deviceId) {
    try {
      this.validateDeviceId(deviceId);
      const devices = await this.adb.listDevices();
      const device = devices.find(d => d.id === deviceId);
      return device && device.status === 'device';
    } catch (error) {
      return false;
    }
  }

  async listDevices() {
    try {
      const devices = await this.adb.listDevices();

      // Update connected devices map and classify device states
      const connectedDevices = [];
      const unauthorizedDevices = [];
      const offlineDevices = [];

      for (const device of devices) {
        switch (device.status) {
          case 'device':
            this.connectedDevices.set(device.id, device);
            connectedDevices.push(device);
            break;
          case 'unauthorized':
            unauthorizedDevices.push(device);
            break;
          case 'offline':
            offlineDevices.push(device);
            break;
        }
      }

      // Log device status for debugging
      console.log(`Devices found: ${devices.length} (Connected: ${connectedDevices.length}, Unauthorized: ${unauthorizedDevices.length}, Offline: ${offlineDevices.length})`);

      return devices;
    } catch (error) {
      console.error('Failed to list devices:', error);
      throw new DeviceError(
        'Failed to list connected devices. Please check ADB connection.',
        ErrorTypes.ADB_ERROR
      );
    }
  }

  async connectDevice(deviceId) {
    try {
      this.validateDeviceId(deviceId);

      // Check if device is in the list first
      const devices = await this.listDevices();
      const device = devices.find(d => d.id === deviceId);

      if (!device) {
        throw new DeviceError(
          `Device ${deviceId} not found. Please check the connection.`,
          ErrorTypes.DEVICE_NOT_FOUND,
          deviceId
        );
      }

      if (device.status === 'unauthorized') {
        throw new DeviceError(
          `Device ${deviceId} is unauthorized. Please accept USB debugging authorization on the device.`,
          ErrorTypes.DEVICE_UNAUTHORIZED,
          deviceId
        );
      }

      if (device.status === 'offline') {
        throw new DeviceError(
          `Device ${deviceId} is offline. Please check the connection and try again.`,
          ErrorTypes.DEVICE_OFFLINE,
          deviceId
        );
      }

      const deviceInfo = await this.adb.getDeviceInfo(deviceId);
      this.connectedDevices.set(deviceId, deviceInfo);

      console.log(`Successfully connected to device: ${deviceId}`);
      return deviceInfo;
    } catch (error) {
      console.error('Failed to connect device:', error);

      if (error instanceof DeviceError) {
        throw error;
      }

      throw new DeviceError(
        `Failed to connect to device ${deviceId}: ${error.message}`,
        ErrorTypes.ADB_ERROR,
        deviceId
      );
    }
  }

  async disconnectDevice(deviceId) {
    try {
      this.connectedDevices.delete(deviceId);
      return { success: true };
    } catch (error) {
      console.error('Failed to disconnect device:', error);
      throw error;
    }
  }

  async getDeviceInfo(deviceId) {
    try {
      return await this.adb.getDeviceInfo(deviceId);
    } catch (error) {
      console.error('Failed to get device info:', error);
      throw error;
    }
  }

  async listApplications(deviceId) {
    try {
      // Get all installed packages
      const packagesOutput = await this.adb.executeCommand('shell pm list packages -f', deviceId);
      const packages = packagesOutput.split('\n')
        .filter(line => line.startsWith('package:'))
        .map(line => {
          const match = line.match(/package:(.+)=(.+)/);
          if (match) {
            return {
              apkPath: match[1],
              packageName: match[2]
            };
          }
          return null;
        })
        .filter(pkg => pkg !== null);

      // Get application details
      const applications = [];
      for (const pkg of packages) {
        try {
          // Get app label
          const labelOutput = await this.adb.executeCommand(
            `shell pm dump ${pkg.packageName} | grep -A1 "labelRes"`, 
            deviceId
          ).catch(() => '');

          // Get version info
          const versionOutput = await this.adb.executeCommand(
            `shell dumpsys package ${pkg.packageName} | grep versionName`, 
            deviceId
          ).catch(() => '');

          // Get install time
          const installOutput = await this.adb.executeCommand(
            `shell dumpsys package ${pkg.packageName} | grep firstInstallTime`, 
            deviceId
          ).catch(() => '');

          // Get app size
          const sizeOutput = await this.adb.executeCommand(
            `shell du -sh "${pkg.apkPath}"`, 
            deviceId
          ).catch(() => '');

          const app = {
            packageName: pkg.packageName,
            apkPath: pkg.apkPath,
            label: this.extractAppLabel(labelOutput, pkg.packageName),
            version: this.extractVersion(versionOutput),
            installTime: this.extractInstallTime(installOutput),
            size: this.extractSize(sizeOutput),
            isSystemApp: pkg.apkPath.includes('/system/'),
            isUserApp: !pkg.apkPath.includes('/system/')
          };

          applications.push(app);
        } catch (error) {
          console.warn(`Failed to get details for ${pkg.packageName}:`, error.message);
        }
      }

      return applications.sort((a, b) => a.label.localeCompare(b.label));
    } catch (error) {
      console.error('Failed to list applications:', error);
      throw error;
    }
  }

  extractAppLabel(labelOutput, packageName) {
    // Try to extract app label, fallback to package name
    const lines = labelOutput.split('\n');
    for (const line of lines) {
      if (line.includes('=') && !line.includes('labelRes')) {
        const label = line.split('=')[1]?.trim();
        if (label && label !== packageName) {
          return label;
        }
      }
    }
    return packageName.split('.').pop() || packageName;
  }

  extractVersion(versionOutput) {
    const match = versionOutput.match(/versionName=([^\s]+)/);
    return match ? match[1] : 'Unknown';
  }

  extractInstallTime(installOutput) {
    const match = installOutput.match(/firstInstallTime=([^\s]+)/);
    if (match) {
      try {
        return new Date(match[1]).toISOString();
      } catch {
        return match[1];
      }
    }
    return 'Unknown';
  }

  extractSize(sizeOutput) {
    const match = sizeOutput.match(/^([^\s]+)/);
    return match ? match[1] : 'Unknown';
  }

  async installApplication(deviceId, apkPath) {
    try {
      const output = await this.adb.executeCommand(`install "${apkPath}"`, deviceId);
      
      if (output.includes('Success')) {
        return { success: true, message: 'Application installed successfully' };
      } else {
        throw new Error(output);
      }
    } catch (error) {
      console.error('Failed to install application:', error);
      throw error;
    }
  }

  async uninstallApplication(deviceId, packageName) {
    try {
      const output = await this.adb.executeCommand(`uninstall ${packageName}`, deviceId);
      
      if (output.includes('Success')) {
        return { success: true, message: 'Application uninstalled successfully' };
      } else {
        throw new Error(output);
      }
    } catch (error) {
      console.error('Failed to uninstall application:', error);
      throw error;
    }
  }

  async backupApplication(deviceId, packageName, outputPath) {
    try {
      // Get APK path
      const pathOutput = await this.adb.executeCommand(
        `shell pm path ${packageName}`, 
        deviceId
      );
      
      const match = pathOutput.match(/package:(.+)/);
      if (!match) {
        throw new Error('Could not find APK path');
      }

      const apkPath = match[1];
      const backupFileName = `${packageName}.apk`;
      const localBackupPath = path.join(outputPath, backupFileName);

      // Pull the APK
      await this.adb.pullFile(deviceId, apkPath, localBackupPath);

      return { 
        success: true, 
        backupPath: localBackupPath,
        message: 'Application backed up successfully' 
      };
    } catch (error) {
      console.error('Failed to backup application:', error);
      throw error;
    }
  }

  async takeScreenshot(deviceId) {
    try {
      const tempDir = os.tmpdir();
      const timestamp = Date.now();
      const remotePath = `/sdcard/screenshot_${timestamp}.png`;
      const localPath = path.join(tempDir, `screenshot_${timestamp}.png`);

      // Take screenshot on device
      await this.adb.executeCommand(`shell screencap -p ${remotePath}`, deviceId);

      // Pull screenshot to local machine
      await this.adb.pullFile(deviceId, remotePath, localPath);

      // Clean up remote file
      await this.adb.executeCommand(`shell rm ${remotePath}`, deviceId);

      return {
        success: true,
        imagePath: localPath,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Failed to take screenshot:', error);
      return { success: false, error: error.message };
    }
  }

  async saveScreenshot(imagePath) {
    try {
      const { dialog } = require('electron');
      const result = await dialog.showSaveDialog({
        defaultPath: `screenshot_${Date.now()}.png`,
        filters: [
          { name: 'PNG Images', extensions: ['png'] },
          { name: 'All Files', extensions: ['*'] }
        ]
      });

      if (!result.canceled && result.filePath) {
        await fs.copy(imagePath, result.filePath);
        return { success: true, path: result.filePath };
      }

      return { success: false, error: 'Save cancelled' };
    } catch (error) {
      console.error('Failed to save screenshot:', error);
      return { success: false, error: error.message };
    }
  }

  async startLogcat(deviceId, options = {}) {
    try {
      const { level = 'V', filter = '' } = options;

      // Clear existing logcat
      await this.adb.executeCommand('shell logcat -c', deviceId);

      // Start logcat stream
      let command = 'shell logcat';
      if (level !== 'V') {
        command += ` *:${level}`;
      }

      this.logcatProcess = this.adb.startLogcatStream(deviceId, command);

      return { success: true };
    } catch (error) {
      console.error('Failed to start logcat:', error);
      return { success: false, error: error.message };
    }
  }

  async stopLogcat(deviceId) {
    try {
      if (this.logcatProcess) {
        this.logcatProcess.kill();
        this.logcatProcess = null;
      }
      return { success: true };
    } catch (error) {
      console.error('Failed to stop logcat:', error);
      return { success: false, error: error.message };
    }
  }

  async saveLogcat(logs) {
    try {
      const { dialog } = require('electron');
      const result = await dialog.showSaveDialog({
        defaultPath: `logcat_${Date.now()}.txt`,
        filters: [
          { name: 'Text Files', extensions: ['txt'] },
          { name: 'Log Files', extensions: ['log'] },
          { name: 'All Files', extensions: ['*'] }
        ]
      });

      if (!result.canceled && result.filePath) {
        const logContent = logs.map(log =>
          `${log.time} ${log.level}/${log.tag}: ${log.message}`
        ).join('\n');

        await fs.writeFile(result.filePath, logContent);
        return { success: true, path: result.filePath };
      }

      return { success: false, error: 'Save cancelled' };
    } catch (error) {
      console.error('Failed to save logcat:', error);
      return { success: false, error: error.message };
    }
  }

  async executeShell(deviceId, command) {
    try {
      const output = await this.adb.executeCommand(`shell ${command}`, deviceId);
      return { success: true, output };
    } catch (error) {
      console.error('Failed to execute shell command:', error);
      return { success: false, error: error.message, output: error.message };
    }
  }

  async toggleWifi(deviceId) {
    try {
      // Get current WiFi state
      const wifiState = await this.adb.executeCommand('shell settings get global wifi_on', deviceId);
      const isEnabled = wifiState.trim() === '1';

      // Toggle WiFi
      const newState = isEnabled ? '0' : '1';
      await this.adb.executeCommand(`shell settings put global wifi_on ${newState}`, deviceId);

      return { success: true, enabled: !isEnabled };
    } catch (error) {
      console.error('Failed to toggle WiFi:', error);
      return { success: false, error: error.message };
    }
  }

  async getLogcat(deviceId, options = {}) {
    try {
      const {
        filter = '',
        priority = 'V', // Verbose
        lines = 1000,
        clear = false
      } = options;

      let command = 'shell logcat';
      
      if (clear) {
        await this.adb.executeCommand('shell logcat -c', deviceId);
        return { success: true, message: 'Logcat cleared' };
      }

      // Add priority filter
      if (priority && priority !== 'V') {
        command += ` *:${priority}`;
      }

      // Add package filter
      if (filter) {
        command += ` | grep "${filter}"`;
      }

      // Limit lines
      command += ` | tail -${lines}`;

      const output = await this.adb.executeCommand(command, deviceId);
      
      // Parse logcat entries
      const entries = this.parseLogcatOutput(output);

      return {
        success: true,
        entries: entries,
        totalLines: entries.length
      };
    } catch (error) {
      console.error('Failed to get logcat:', error);
      throw error;
    }
  }

  parseLogcatOutput(output) {
    const lines = output.split('\n');
    const entries = [];

    for (const line of lines) {
      if (line.trim()) {
        // Parse logcat format: MM-DD HH:MM:SS.mmm PID TID PRIORITY TAG: MESSAGE
        const match = line.match(/^(\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})\s+(\d+)\s+(\d+)\s+([VDIWEF])\s+([^:]+):\s*(.*)$/);
        
        if (match) {
          entries.push({
            timestamp: match[1],
            pid: match[2],
            tid: match[3],
            priority: match[4],
            tag: match[5],
            message: match[6],
            raw: line
          });
        } else {
          // If line doesn't match format, treat as continuation of previous message
          if (entries.length > 0) {
            entries[entries.length - 1].message += '\n' + line;
            entries[entries.length - 1].raw += '\n' + line;
          }
        }
      }
    }

    return entries;
  }

  async rebootDevice(deviceId, mode = 'normal') {
    try {
      let command = 'reboot';
      
      switch (mode) {
        case 'recovery':
          command = 'reboot recovery';
          break;
        case 'bootloader':
          command = 'reboot bootloader';
          break;
        case 'fastboot':
          command = 'reboot bootloader';
          break;
        default:
          command = 'reboot';
      }

      await this.adb.executeCommand(command, deviceId);
      
      return { 
        success: true, 
        message: `Device rebooting to ${mode} mode` 
      };
    } catch (error) {
      console.error('Failed to reboot device:', error);
      throw error;
    }
  }

  async getDeviceProperties(deviceId) {
    try {
      const output = await this.adb.executeCommand('shell getprop', deviceId);
      const properties = {};
      
      const lines = output.split('\n');
      for (const line of lines) {
        const match = line.match(/\[([^\]]+)\]:\s*\[([^\]]*)\]/);
        if (match) {
          properties[match[1]] = match[2];
        }
      }

      return {
        success: true,
        properties: properties
      };
    } catch (error) {
      console.error('Failed to get device properties:', error);
      throw error;
    }
  }

  async sendKeyEvent(deviceId, keyCode) {
    try {
      await this.adb.executeCommand(`shell input keyevent ${keyCode}`, deviceId);
      return { success: true };
    } catch (error) {
      console.error('Failed to send key event:', error);
      throw error;
    }
  }

  async sendText(deviceId, text) {
    try {
      // Escape special characters
      const escapedText = text.replace(/['"\\]/g, '\\$&');
      await this.adb.executeCommand(`shell input text "${escapedText}"`, deviceId);
      return { success: true };
    } catch (error) {
      console.error('Failed to send text:', error);
      throw error;
    }
  }

  async launchApp(deviceId, packageName) {
    try {
      // Get the main activity for the package
      const output = await this.adb.executeCommand(
        `shell pm dump ${packageName} | grep -A 1 "android.intent.action.MAIN"`,
        deviceId
      );

      // Extract activity name
      const activityMatch = output.match(/([a-zA-Z0-9_.]+\/[a-zA-Z0-9_.]+)/);

      if (activityMatch) {
        const activity = activityMatch[1];
        await this.adb.executeCommand(`shell am start -n ${activity}`, deviceId);
        return { success: true, message: 'Application launched successfully' };
      } else {
        // Fallback: try to launch using monkey
        await this.adb.executeCommand(`shell monkey -p ${packageName} -c android.intent.category.LAUNCHER 1`, deviceId);
        return { success: true, message: 'Application launched using monkey' };
      }
    } catch (error) {
      console.error('Failed to launch application:', error);
      return { success: false, error: error.message };
    }
  }

  async getDetailedDeviceInfo(deviceId) {
    try {
      const info = await this.getDeviceInfo(deviceId);

      // Get additional hardware information
      const [
        memoryInfo,
        storageInfo,
        batteryInfo,
        cpuInfo
      ] = await Promise.all([
        this.adb.executeCommand('shell cat /proc/meminfo | head -3', deviceId).catch(() => ''),
        this.adb.executeCommand('shell df /data', deviceId).catch(() => ''),
        this.adb.executeCommand('shell dumpsys battery', deviceId).catch(() => ''),
        this.adb.executeCommand('shell cat /proc/cpuinfo | head -10', deviceId).catch(() => '')
      ]);

      // Parse memory info
      const memMatch = memoryInfo.match(/MemTotal:\s+(\d+)\s+kB/);
      const totalMemory = memMatch ? `${Math.round(parseInt(memMatch[1]) / 1024)} MB` : 'Unknown';

      // Parse storage info
      const storageMatch = storageInfo.match(/\s+(\d+)\s+\d+\s+\d+\s+\d+%/);
      const totalStorage = storageMatch ? `${Math.round(parseInt(storageMatch[1]) / 1024 / 1024)} GB` : 'Unknown';

      // Parse battery info
      const batteryMatch = batteryInfo.match(/level: (\d+)/);
      const batteryLevel = batteryMatch ? parseInt(batteryMatch[1]) : 0;

      // Parse temperature
      const tempMatch = batteryInfo.match(/temperature: (\d+)/);
      const temperature = tempMatch ? `${parseInt(tempMatch[1]) / 10}°C` : 'Unknown';

      return {
        ...info,
        totalMemory,
        totalStorage,
        batteryLevel,
        temperature
      };
    } catch (error) {
      console.error('Failed to get detailed device info:', error);
      throw error;
    }
  }
}

module.exports = DeviceService;
