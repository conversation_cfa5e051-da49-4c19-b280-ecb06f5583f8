import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  IconButton,
  Breadcrumbs,
  Link,
  Chip,
  Alert,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Checkbox,
  ListItemButton,
  Tooltip,
  LinearProgress,
  Divider,
  FormControlLabel,
  Switch,
  CircularProgress,
  Stack
} from '@mui/material';
import {
  validateInput,
  validateFileOperation,
  createRetryHandler,
  checkDeviceConnection,
  logError,
  getErrorMessage
} from '../utils/errorHandler';
import { useNotifications } from './NotificationSystem';
import {
  Folder,
  InsertDriveFile,
  ArrowBack,
  Refresh,
  Home,
  Android,
  Computer,
  MoreVert,
  Delete,
  Edit,
  FileCopy,
  DriveFileMove,
  CreateNewFolder,
  Upload,
  Download,
  SelectAll,
  Deselect,
  Info,
  Image,
  VideoFile,
  AudioFile,
  PictureAsPdf,
  Archive,
  Code
} from '@mui/icons-material';

function FileBrowser({ selectedDevice, onShowNotification }) {
  const [devicePath, setDevicePath] = useState('/sdcard');
  const [pcPath, setPcPath] = useState('');
  const [deviceFiles, setDeviceFiles] = useState([]);
  const [pcFiles, setPcFiles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [deviceLoading, setDeviceLoading] = useState(false);
  const [pcLoading, setPcLoading] = useState(false);
  const [selectedDeviceFiles, setSelectedDeviceFiles] = useState(new Set());
  const [selectedPcFiles, setSelectedPcFiles] = useState(new Set());
  const [contextMenu, setContextMenu] = useState(null);
  const [contextMenuType, setContextMenuType] = useState(null);
  const [contextMenuFile, setContextMenuFile] = useState(null);
  const [showCreateFolder, setShowCreateFolder] = useState(false);
  const [newFolderName, setNewFolderName] = useState('');
  const [showRename, setShowRename] = useState(false);
  const [renameFile, setRenameFile] = useState(null);
  const [newFileName, setNewFileName] = useState('');
  const [showFileInfo, setShowFileInfo] = useState(false);
  const [fileInfo, setFileInfo] = useState(null);
  const [transferProgress, setTransferProgress] = useState(null);
  const [showHiddenFiles, setShowHiddenFiles] = useState(false);
  const [sortBy, setSortBy] = useState('name');
  const [sortOrder, setSortOrder] = useState('asc');
  const [deviceConnectionError, setDeviceConnectionError] = useState(null);
  const [retryCount, setRetryCount] = useState(0);
  const fileInputRef = useRef(null);

  // Use enhanced notification system
  const { showError, showSuccess, showWarning, showInfo } = useNotifications();

  useEffect(() => {
    if (selectedDevice) {
      loadDeviceFiles(devicePath);
    }
    loadPcFiles(pcPath);
  }, [selectedDevice, devicePath, pcPath]);

  useEffect(() => {
    // Listen for transfer progress updates
    const handleTransferProgress = (data) => {
      setTransferProgress(data);
    };

    const handleTransferComplete = (data) => {
      setTransferProgress(null);
      onShowNotification(`Transfer completed: ${data.files?.length || 0} files`, 'success');
      // Refresh file lists
      if (selectedDevice) {
        loadDeviceFiles(devicePath);
      }
      loadPcFiles(pcPath);
    };

    const handleTransferError = (data) => {
      setTransferProgress(null);
      onShowNotification(`Transfer failed: ${data.error}`, 'error');
    };

    // Add event listeners if they exist
    if (window.electronAPI?.transfer?.onProgress) {
      window.electronAPI.transfer.onProgress(handleTransferProgress);
      window.electronAPI.transfer.onComplete(handleTransferComplete);
      window.electronAPI.transfer.onError(handleTransferError);
    }

    return () => {
      // Cleanup listeners if needed
    };
  }, [selectedDevice, devicePath, pcPath, onShowNotification]);

  const loadDeviceFiles = createRetryHandler(async (path) => {
    if (!selectedDevice) return;

    try {
      // Validate inputs
      validateInput.required(selectedDevice.id, 'Device ID');
      validateInput.path(path);

      setDeviceLoading(true);
      setDeviceConnectionError(null);

      // Check device connection first
      const connectionCheck = await checkDeviceConnection(selectedDevice.id);
      if (!connectionCheck.connected) {
        throw new Error(`Device not connected: ${connectionCheck.error}`);
      }

      const files = await window.electronAPI.file.list(selectedDevice.id, path);
      setDeviceFiles(sortFiles(filterFiles(files)));
      setSelectedDeviceFiles(new Set());
      setRetryCount(0); // Reset retry count on success

      if (retryCount > 0) {
        showSuccess('Device files loaded successfully');
      }
    } catch (error) {
      logError(error, 'Loading device files', { deviceId: selectedDevice.id, path });
      setDeviceConnectionError(error.message);

      showError(error, 'Loading device files', {
        retryAction: () => loadDeviceFiles(path),
        suggestions: [
          'Check if the device is still connected',
          'Ensure USB debugging is enabled',
          'Try refreshing the connection',
          'Navigate to a different folder'
        ]
      });

      setRetryCount(prev => prev + 1);
      throw error; // Re-throw for retry handler
    } finally {
      setDeviceLoading(false);
    }
  }, 3, 2000);

  const loadPcFiles = createRetryHandler(async (path) => {
    try {
      // Validate path if provided
      if (path) {
        validateInput.path(path);
      }

      setPcLoading(true);
      const files = await window.electronAPI.file.listPC(path);
      setPcFiles(sortFiles(filterFiles(files)));
      setSelectedPcFiles(new Set());
    } catch (error) {
      logError(error, 'Loading PC files', { path });

      showError(error, 'Loading PC files', {
        retryAction: () => loadPcFiles(path),
        suggestions: [
          'Check if the folder exists',
          'Verify you have read permissions',
          'Try navigating to a different folder',
          'Check available disk space'
        ]
      });

      throw error; // Re-throw for retry handler
    } finally {
      setPcLoading(false);
    }
  }, 2, 1000);

  const filterFiles = (files) => {
    if (showHiddenFiles) return files;
    return files.filter(file => !file.name.startsWith('.'));
  };

  const sortFiles = (files) => {
    return [...files].sort((a, b) => {
      // Always put directories first
      if (a.isDirectory !== b.isDirectory) {
        return a.isDirectory ? -1 : 1;
      }

      let comparison = 0;
      switch (sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'size':
          comparison = (a.size || 0) - (b.size || 0);
          break;
        case 'modified':
          comparison = new Date(a.modified || 0) - new Date(b.modified || 0);
          break;
        default:
          comparison = a.name.localeCompare(b.name);
      }

      return sortOrder === 'asc' ? comparison : -comparison;
    });
  };

  const getFileIcon = (file) => {
    if (file.isDirectory) return <Folder />;

    const ext = file.name.split('.').pop()?.toLowerCase();
    switch (ext) {
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
      case 'webp':
        return <Image />;
      case 'mp4':
      case 'avi':
      case 'mkv':
      case 'mov':
      case 'wmv':
        return <VideoFile />;
      case 'mp3':
      case 'wav':
      case 'flac':
      case 'aac':
        return <AudioFile />;
      case 'pdf':
        return <PictureAsPdf />;
      case 'zip':
      case 'rar':
      case '7z':
      case 'tar':
      case 'gz':
        return <Archive />;
      case 'js':
      case 'html':
      case 'css':
      case 'py':
      case 'java':
      case 'cpp':
      case 'c':
        return <Code />;
      default:
        return <InsertDriveFile />;
    }
  };

  const formatFileSize = (bytes) => {
    if (!bytes) return '';
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
  };

  const formatDate = (dateString) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleString();
  };

  // File operations
  const handleFileClick = (file, isDevice) => {
    if (file.isDirectory) {
      if (isDevice) {
        const newPath = devicePath === '/' ? `/${file.name}` : `${devicePath}/${file.name}`;
        setDevicePath(newPath);
      } else {
        const newPath = pcPath ? `${pcPath}/${file.name}` : file.name;
        setPcPath(newPath);
      }
    } else {
      // Handle file selection
      const selectedSet = isDevice ? selectedDeviceFiles : selectedPcFiles;
      const setSelected = isDevice ? setSelectedDeviceFiles : setSelectedPcFiles;

      const newSelected = new Set(selectedSet);
      if (newSelected.has(file.name)) {
        newSelected.delete(file.name);
      } else {
        newSelected.add(file.name);
      }
      setSelected(newSelected);
    }
  };

  const handleContextMenu = (event, file, isDevice) => {
    event.preventDefault();
    setContextMenu({
      mouseX: event.clientX - 2,
      mouseY: event.clientY - 4,
    });
    setContextMenuType(isDevice ? 'device' : 'pc');
    setContextMenuFile(file);
  };

  const closeContextMenu = () => {
    setContextMenu(null);
    setContextMenuType(null);
    setContextMenuFile(null);
  };

  const handleCreateFolder = async () => {
    if (!newFolderName.trim()) return;

    try {
      if (contextMenuType === 'device') {
        await window.electronAPI.file.createFolder(selectedDevice.id, devicePath, newFolderName);
        loadDeviceFiles(devicePath);
      } else {
        await window.electronAPI.file.createFolderPC(pcPath, newFolderName);
        loadPcFiles(pcPath);
      }
      onShowNotification('Folder created successfully', 'success');
    } catch (error) {
      onShowNotification('Failed to create folder', 'error');
    }

    setShowCreateFolder(false);
    setNewFolderName('');
    closeContextMenu();
  };

  const handleRename = async () => {
    try {
      // Validate inputs
      validateInput.required(newFileName.trim(), 'New file name');
      validateInput.required(renameFile, 'File to rename');
      validateFileOperation.rename(renameFile.name, newFileName.trim());

      if (contextMenuType === 'device') {
        validateInput.deviceId(selectedDevice?.id);

        const oldPath = devicePath === '/' ? `/${renameFile.name}` : `${devicePath}/${renameFile.name}`;
        const newPath = devicePath === '/' ? `/${newFileName.trim()}` : `${devicePath}/${newFileName.trim()}`;

        await window.electronAPI.file.rename(selectedDevice.id, oldPath, newPath);
        await loadDeviceFiles(devicePath);
      } else {
        const oldPath = pcPath ? `${pcPath}/${renameFile.name}` : renameFile.name;
        const newPath = pcPath ? `${pcPath}/${newFileName.trim()}` : newFileName.trim();

        await window.electronAPI.file.renamePC(oldPath, newPath);
        await loadPcFiles(pcPath);
      }

      showSuccess(`File renamed from "${renameFile.name}" to "${newFileName.trim()}"`);
    } catch (error) {
      logError(error, 'Renaming file', {
        oldName: renameFile?.name,
        newName: newFileName,
        type: contextMenuType
      });

      showError(error, 'Renaming file', {
        retryAction: () => handleRename(),
        suggestions: [
          'Check if the new name is valid',
          'Ensure the file is not in use',
          'Verify you have write permissions',
          'Try a different file name'
        ]
      });
    }

    setShowRename(false);
    setRenameFile(null);
    setNewFileName('');
    closeContextMenu();
  };

  const handleDelete = async () => {
    try {
      const selectedFiles = contextMenuType === 'device' ? selectedDeviceFiles : selectedPcFiles;
      const filesToDelete = selectedFiles.size > 0 ? Array.from(selectedFiles) : [contextMenuFile.name];

      // Validate inputs
      validateFileOperation.delete(filesToDelete);

      if (!window.confirm(`Are you sure you want to delete ${filesToDelete.length} item(s)?\n\nThis action cannot be undone.`)) {
        return;
      }

      if (contextMenuType === 'device') {
        validateInput.deviceId(selectedDevice?.id);

        const paths = filesToDelete.map(name => {
          const path = devicePath === '/' ? `/${name}` : `${devicePath}/${name}`;
          validateInput.path(path);
          return path;
        });

        await window.electronAPI.file.delete(selectedDevice.id, paths);
        await loadDeviceFiles(devicePath);
      } else {
        const paths = filesToDelete.map(name => {
          const path = pcPath ? `${pcPath}/${name}` : name;
          validateInput.path(path);
          return path;
        });

        await window.electronAPI.file.deletePC(paths);
        await loadPcFiles(pcPath);
      }

      showSuccess(`Successfully deleted ${filesToDelete.length} item(s)`);
    } catch (error) {
      logError(error, 'Deleting files', {
        files: filesToDelete,
        type: contextMenuType
      });

      showError(error, 'Deleting files', {
        retryAction: () => handleDelete(),
        suggestions: [
          'Check if the files are not in use',
          'Verify you have delete permissions',
          'Ensure the files still exist',
          'Try deleting files one by one'
        ]
      });
    }

    closeContextMenu();
  };

  const handleUpload = async () => {
    try {
      const selectedFiles = Array.from(selectedPcFiles);

      // Validate inputs
      validateFileOperation.upload(selectedFiles, devicePath);
      validateInput.deviceId(selectedDevice?.id);

      // Check device connection
      const connectionCheck = await checkDeviceConnection(selectedDevice.id);
      if (!connectionCheck.connected) {
        throw new Error(`Device not connected: ${connectionCheck.error}`);
      }

      const localPaths = selectedFiles.map(name => {
        const path = pcPath ? `${pcPath}/${name}` : name;
        validateInput.path(path);
        return path;
      });

      const result = await window.electronAPI.transfer.upload(
        selectedDevice.id,
        localPaths,
        devicePath
      );

      showInfo(`Started upload of ${selectedFiles.length} file(s) to device`);
    } catch (error) {
      logError(error, 'Starting upload', {
        files: Array.from(selectedPcFiles),
        destination: devicePath
      });

      showError(error, 'Starting upload', {
        retryAction: () => handleUpload(),
        suggestions: [
          'Check device connection',
          'Ensure sufficient storage space on device',
          'Verify file permissions',
          'Try uploading fewer files at once'
        ]
      });
    }

    closeContextMenu();
  };

  const handleDownload = async () => {
    try {
      const selectedFiles = Array.from(selectedDeviceFiles);

      // Validate inputs
      validateFileOperation.download(selectedFiles, pcPath || '');
      validateInput.deviceId(selectedDevice?.id);

      // Check device connection
      const connectionCheck = await checkDeviceConnection(selectedDevice.id);
      if (!connectionCheck.connected) {
        throw new Error(`Device not connected: ${connectionCheck.error}`);
      }

      const remotePaths = selectedFiles.map(name => {
        const path = devicePath === '/' ? `/${name}` : `${devicePath}/${name}`;
        validateInput.path(path);
        return path;
      });

      const result = await window.electronAPI.transfer.download(
        selectedDevice.id,
        remotePaths,
        pcPath || ''
      );

      showInfo(`Started download of ${selectedFiles.length} file(s) from device`);
    } catch (error) {
      logError(error, 'Starting download', {
        files: Array.from(selectedDeviceFiles),
        destination: pcPath
      });

      showError(error, 'Starting download', {
        retryAction: () => handleDownload(),
        suggestions: [
          'Check device connection',
          'Ensure sufficient storage space on PC',
          'Verify file permissions',
          'Try downloading fewer files at once'
        ]
      });
    }

    closeContextMenu();
  };

  const handleSelectAll = (isDevice) => {
    const files = isDevice ? deviceFiles : pcFiles;
    const setSelected = isDevice ? setSelectedDeviceFiles : setSelectedPcFiles;
    setSelected(new Set(files.map(f => f.name)));
    closeContextMenu();
  };

  const handleDeselectAll = (isDevice) => {
    const setSelected = isDevice ? setSelectedDeviceFiles : setSelectedPcFiles;
    setSelected(new Set());
    closeContextMenu();
  };

  const handleShowInfo = async () => {
    try {
      let info;
      if (contextMenuType === 'device') {
        const filePath = devicePath === '/' ? `/${contextMenuFile.name}` : `${devicePath}/${contextMenuFile.name}`;
        info = await window.electronAPI.file.getInfo(selectedDevice.id, filePath);
      } else {
        const filePath = pcPath ? `${pcPath}/${contextMenuFile.name}` : contextMenuFile.name;
        info = await window.electronAPI.file.getInfoPC(filePath);
      }
      setFileInfo(info);
      setShowFileInfo(true);
    } catch (error) {
      onShowNotification('Failed to get file info', 'error');
    }
    closeContextMenu();
  };

  const navigateUp = (isDevice) => {
    if (isDevice) {
      if (devicePath !== '/') {
        const parentPath = devicePath.split('/').slice(0, -1).join('/') || '/';
        setDevicePath(parentPath);
      }
    } else {
      if (pcPath) {
        const parentPath = pcPath.split('/').slice(0, -1).join('/');
        setPcPath(parentPath);
      }
    }
  };

  const navigateToPath = (path, isDevice) => {
    if (isDevice) {
      setDevicePath(path);
    } else {
      setPcPath(path);
    }
  };

  const handleFileUploadSelect = () => {
    fileInputRef.current?.click();
  };

  const handleFileUploadChange = async (event) => {
    const files = Array.from(event.target.files);
    if (files.length === 0) return;

    try {
      const filePaths = files.map(file => file.path);
      const result = await window.electronAPI.transfer.upload(
        selectedDevice.id,
        filePaths,
        devicePath
      );

      onShowNotification(`Started upload of ${files.length} file(s)`, 'info');
    } catch (error) {
      onShowNotification('Failed to start upload', 'error');
    }

    // Reset file input
    event.target.value = '';
  };

  const renderFileList = (files, isDevice = false) => {
    const selectedSet = isDevice ? selectedDeviceFiles : selectedPcFiles;

    return (
      <List dense>
        {files.map((file, index) => (
          <ListItemButton
            key={index}
            selected={selectedSet.has(file.name)}
            onClick={() => handleFileClick(file, isDevice)}
            onContextMenu={(e) => handleContextMenu(e, file, isDevice)}
            sx={{
              '&:hover': {
                backgroundColor: 'action.hover',
              },
              '&.Mui-selected': {
                backgroundColor: 'primary.light',
                '&:hover': {
                  backgroundColor: 'primary.main',
                },
              },
            }}
          >
            <ListItemIcon>
              <Checkbox
                edge="start"
                checked={selectedSet.has(file.name)}
                tabIndex={-1}
                disableRipple
                size="small"
                sx={{ mr: 1 }}
              />
              {getFileIcon(file)}
            </ListItemIcon>
            <ListItemText
              primaryTypographyProps={{ component: 'div' }}
              secondaryTypographyProps={{ component: 'div' }}
              primary={
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Typography variant="body2" noWrap>
                    {file.name}
                  </Typography>
                  <IconButton
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleContextMenu(e, file, isDevice);
                    }}
                  >
                    <MoreVert fontSize="small" />
                  </IconButton>
                </Box>
              }
              secondary={
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Typography variant="caption" color="text.secondary">
                    {file.isDirectory ? 'Folder' : formatFileSize(file.size)}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {formatDate(file.modified)}
                  </Typography>
                </Box>
              }
            />
          </ListItemButton>
        ))}
        {files.length === 0 && (
          <ListItem>
            <ListItemText
              primary="No files found"
              secondary="This directory is empty"
              sx={{ textAlign: 'center', color: 'text.secondary' }}
            />
          </ListItem>
        )}
      </List>
    );
  };

  if (!selectedDevice) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Android sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
        <Typography variant="h6" gutterBottom>
          No Device Selected
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Please select a device from the Device Manager to browse files.
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3, height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h4" component="h1">
          File Browser
        </Typography>
        <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
          <FormControlLabel
            control={
              <Switch
                checked={showHiddenFiles}
                onChange={(e) => setShowHiddenFiles(e.target.checked)}
                size="small"
              />
            }
            label="Hidden files"
          />
          <Tooltip title="Upload files">
            <IconButton onClick={handleFileUploadSelect}>
              <Upload />
            </IconButton>
          </Tooltip>
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileUploadChange}
            multiple
            style={{ display: 'none' }}
          />
        </Box>
      </Box>

      {/* Transfer Progress */}
      {transferProgress && (
        <Paper sx={{ p: 2, mb: 2 }}>
          <Typography variant="subtitle2" gutterBottom>
            {transferProgress.type === 'upload' ? 'Uploading' : 'Downloading'}: {transferProgress.currentFile}
          </Typography>
          <LinearProgress
            variant="determinate"
            value={transferProgress.progress || 0}
            sx={{ mb: 1 }}
          />
          <Typography variant="caption" color="text.secondary">
            {transferProgress.transferred} / {transferProgress.total} files
          </Typography>
        </Paper>
      )}

      <Grid container spacing={2} sx={{ flexGrow: 1 }}>
        {/* Device Files */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Android sx={{ mr: 1 }} />
                  <Typography variant="h6">Device Files</Typography>
                  <Chip
                    label={selectedDevice.model || selectedDevice.id}
                    size="small"
                    sx={{ ml: 2 }}
                  />
                </Box>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Tooltip title="Go up">
                    <IconButton size="small" onClick={() => navigateUp(true)}>
                      <ArrowBack />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Refresh">
                    <IconButton size="small" onClick={() => loadDeviceFiles(devicePath)}>
                      <Refresh />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Home">
                    <IconButton size="small" onClick={() => setDevicePath('/sdcard')}>
                      <Home />
                    </IconButton>
                  </Tooltip>
                </Box>
              </Box>
              <Breadcrumbs>
                <Link
                  component="button"
                  variant="body2"
                  onClick={() => setDevicePath('/sdcard')}
                >
                  <Home sx={{ mr: 0.5 }} fontSize="inherit" />
                  Storage
                </Link>
                {devicePath.split('/').filter(Boolean).map((segment, index, array) => (
                  <Typography key={index} color="text.primary" variant="body2">
                    {segment}
                  </Typography>
                ))}
              </Breadcrumbs>
            </Box>
            <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
              {renderFileList(deviceFiles, true)}
            </Box>
          </Paper>
        </Grid>

        {/* PC Files */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Computer sx={{ mr: 1 }} />
                  <Typography variant="h6">PC Files</Typography>
                </Box>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Tooltip title="Go up">
                    <IconButton size="small" onClick={() => navigateUp(false)}>
                      <ArrowBack />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Refresh">
                    <IconButton size="small" onClick={() => loadPcFiles(pcPath)}>
                      <Refresh />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Home">
                    <IconButton size="small" onClick={() => setPcPath('')}>
                      <Home />
                    </IconButton>
                  </Tooltip>
                </Box>
              </Box>
              <Breadcrumbs>
                <Link
                  component="button"
                  variant="body2"
                  onClick={() => setPcPath('')}
                >
                  <Home sx={{ mr: 0.5 }} fontSize="inherit" />
                  Home
                </Link>
                {pcPath && pcPath.split('/').filter(Boolean).map((segment, index, array) => (
                  <Typography key={index} color="text.primary" variant="body2">
                    {segment}
                  </Typography>
                ))}
              </Breadcrumbs>
            </Box>
            <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
              {renderFileList(pcFiles, false)}
            </Box>
          </Paper>
        </Grid>
      </Grid>

      {/* Context Menu */}
      <Menu
        open={contextMenu !== null}
        onClose={closeContextMenu}
        anchorReference="anchorPosition"
        anchorPosition={
          contextMenu !== null
            ? { top: contextMenu.mouseY, left: contextMenu.mouseX }
            : undefined
        }
      >
        <MenuItem onClick={() => { setShowCreateFolder(true); setContextMenuType(contextMenuType); }}>
          <ListItemIcon><CreateNewFolder fontSize="small" /></ListItemIcon>
          <ListItemText>New Folder</ListItemText>
        </MenuItem>
        <Divider />
        <MenuItem onClick={() => handleSelectAll(contextMenuType === 'device')}>
          <ListItemIcon><SelectAll fontSize="small" /></ListItemIcon>
          <ListItemText>Select All</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => handleDeselectAll(contextMenuType === 'device')}>
          <ListItemIcon><Deselect fontSize="small" /></ListItemIcon>
          <ListItemText>Deselect All</ListItemText>
        </MenuItem>
        <Divider />
        {contextMenuFile && (
          <>
            <MenuItem onClick={() => { setShowRename(true); setRenameFile(contextMenuFile); setNewFileName(contextMenuFile.name); }}>
              <ListItemIcon><Edit fontSize="small" /></ListItemIcon>
              <ListItemText>Rename</ListItemText>
            </MenuItem>
            <MenuItem onClick={handleShowInfo}>
              <ListItemIcon><Info fontSize="small" /></ListItemIcon>
              <ListItemText>Properties</ListItemText>
            </MenuItem>
            <Divider />
          </>
        )}
        {contextMenuType === 'pc' && (
          <MenuItem onClick={handleUpload}>
            <ListItemIcon><Upload fontSize="small" /></ListItemIcon>
            <ListItemText>Upload to Device</ListItemText>
          </MenuItem>
        )}
        {contextMenuType === 'device' && (
          <MenuItem onClick={handleDownload}>
            <ListItemIcon><Download fontSize="small" /></ListItemIcon>
            <ListItemText>Download to PC</ListItemText>
          </MenuItem>
        )}
        <Divider />
        <MenuItem onClick={handleDelete} sx={{ color: 'error.main' }}>
          <ListItemIcon><Delete fontSize="small" color="error" /></ListItemIcon>
          <ListItemText>Delete</ListItemText>
        </MenuItem>
      </Menu>

      {/* Create Folder Dialog */}
      <Dialog open={showCreateFolder} onClose={() => setShowCreateFolder(false)}>
        <DialogTitle>Create New Folder</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Folder Name"
            fullWidth
            variant="outlined"
            value={newFolderName}
            onChange={(e) => setNewFolderName(e.target.value)}
            onKeyPress={(e) => {
              if (e.key === 'Enter') {
                handleCreateFolder();
              }
            }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowCreateFolder(false)}>Cancel</Button>
          <Button onClick={handleCreateFolder} variant="contained">Create</Button>
        </DialogActions>
      </Dialog>

      {/* Rename Dialog */}
      <Dialog open={showRename} onClose={() => setShowRename(false)}>
        <DialogTitle>Rename File</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="New Name"
            fullWidth
            variant="outlined"
            value={newFileName}
            onChange={(e) => setNewFileName(e.target.value)}
            onKeyPress={(e) => {
              if (e.key === 'Enter') {
                handleRename();
              }
            }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowRename(false)}>Cancel</Button>
          <Button onClick={handleRename} variant="contained">Rename</Button>
        </DialogActions>
      </Dialog>

      {/* File Info Dialog */}
      <Dialog open={showFileInfo} onClose={() => setShowFileInfo(false)} maxWidth="sm" fullWidth>
        <DialogTitle>File Properties</DialogTitle>
        <DialogContent>
          {fileInfo && (
            <List>
              <ListItem>
                <ListItemText primary="Name" secondary={fileInfo.name} />
              </ListItem>
              <ListItem>
                <ListItemText primary="Size" secondary={formatFileSize(fileInfo.size)} />
              </ListItem>
              <ListItem>
                <ListItemText primary="Type" secondary={fileInfo.isDirectory ? 'Folder' : 'File'} />
              </ListItem>
              <ListItem>
                <ListItemText primary="Modified" secondary={formatDate(fileInfo.modified)} />
              </ListItem>
              <ListItem>
                <ListItemText primary="Path" secondary={fileInfo.path} />
              </ListItem>
              {fileInfo.permissions && (
                <ListItem>
                  <ListItemText primary="Permissions" secondary={fileInfo.permissions} />
                </ListItem>
              )}
            </List>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowFileInfo(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default FileBrowser;
