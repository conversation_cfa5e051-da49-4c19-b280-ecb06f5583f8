const fs = require('fs-extra');
const path = require('path');
const os = require('os');
const mime = require('mime-types');

class FileService {
  constructor() {
    this.homeDirectory = os.homedir();
  }

  async listPCFiles(dirPath = this.homeDirectory) {
    try {
      // Normalize and validate path
      const normalizedPath = path.resolve(dirPath);
      
      // Security check - prevent access outside of allowed directories
      if (!this.isPathAllowed(normalizedPath)) {
        throw new Error('Access denied to this directory');
      }

      const stats = await fs.stat(normalizedPath);
      if (!stats.isDirectory()) {
        throw new Error('Path is not a directory');
      }

      const items = await fs.readdir(normalizedPath);
      const files = [];

      for (const item of items) {
        try {
          const itemPath = path.join(normalizedPath, item);
          const itemStats = await fs.stat(itemPath);
          
          // Skip hidden files on Unix-like systems (optional)
          if (item.startsWith('.') && process.platform !== 'win32') {
            continue;
          }

          const fileInfo = {
            name: item,
            path: itemPath,
            size: itemStats.size,
            isDirectory: itemStats.isDirectory(),
            modified: itemStats.mtime.toISOString(),
            created: itemStats.birthtime.toISOString(),
            permissions: this.getPermissions(itemStats),
            type: this.getFileType(item, itemStats.isDirectory()),
            mimeType: itemStats.isDirectory() ? null : mime.lookup(item) || 'application/octet-stream'
          };

          files.push(fileInfo);
        } catch (error) {
          // Skip files that can't be accessed
          console.warn(`Cannot access ${item}:`, error.message);
        }
      }

      // Sort: directories first, then files, both alphabetically
      return files.sort((a, b) => {
        if (a.isDirectory && !b.isDirectory) return -1;
        if (!a.isDirectory && b.isDirectory) return 1;
        return a.name.localeCompare(b.name, undefined, { numeric: true });
      });

    } catch (error) {
      console.error('Failed to list PC files:', error);
      throw error;
    }
  }

  isPathAllowed(targetPath) {
    // Define allowed root directories
    const allowedRoots = [
      os.homedir(),
      path.resolve('C:\\'), // Windows drives
      path.resolve('D:\\'),
      path.resolve('E:\\'),
      path.resolve('/'), // Unix root (be careful with this)
      path.resolve('/Users'), // macOS users
      path.resolve('/home') // Linux users
    ];

    // Check if the target path is within allowed directories
    return allowedRoots.some(root => {
      try {
        const relative = path.relative(root, targetPath);
        return !relative.startsWith('..') && !path.isAbsolute(relative);
      } catch {
        return false;
      }
    });
  }

  getPermissions(stats) {
    const mode = stats.mode;
    const permissions = {
      readable: !!(mode & parseInt('400', 8)),
      writable: !!(mode & parseInt('200', 8)),
      executable: !!(mode & parseInt('100', 8))
    };
    return permissions;
  }

  getFileType(filename, isDirectory) {
    if (isDirectory) return 'folder';
    
    const ext = path.extname(filename).toLowerCase();
    const typeMap = {
      // Applications
      '.exe': 'application', '.msi': 'application', '.app': 'application', '.deb': 'application', '.rpm': 'application',
      '.apk': 'android-app',
      
      // Images
      '.jpg': 'image', '.jpeg': 'image', '.png': 'image', '.gif': 'image', '.bmp': 'image', 
      '.svg': 'image', '.webp': 'image', '.ico': 'image', '.tiff': 'image', '.tif': 'image',
      
      // Videos
      '.mp4': 'video', '.avi': 'video', '.mkv': 'video', '.mov': 'video', '.wmv': 'video',
      '.flv': 'video', '.webm': 'video', '.m4v': 'video', '.3gp': 'video',
      
      // Audio
      '.mp3': 'audio', '.wav': 'audio', '.flac': 'audio', '.aac': 'audio', '.ogg': 'audio',
      '.wma': 'audio', '.m4a': 'audio', '.opus': 'audio',
      
      // Documents
      '.pdf': 'document', '.doc': 'document', '.docx': 'document', '.xls': 'document', 
      '.xlsx': 'document', '.ppt': 'document', '.pptx': 'document', '.odt': 'document',
      '.ods': 'document', '.odp': 'document',
      
      // Text files
      '.txt': 'text', '.log': 'text', '.md': 'text', '.rtf': 'text',
      
      // Code files
      '.js': 'code', '.html': 'code', '.css': 'code', '.json': 'code', '.xml': 'code',
      '.py': 'code', '.java': 'code', '.cpp': 'code', '.c': 'code', '.h': 'code',
      '.php': 'code', '.rb': 'code', '.go': 'code', '.rs': 'code', '.swift': 'code',
      
      // Archives
      '.zip': 'archive', '.rar': 'archive', '.7z': 'archive', '.tar': 'archive',
      '.gz': 'archive', '.bz2': 'archive', '.xz': 'archive'
    };
    
    return typeMap[ext] || 'file';
  }

  async createFolder(dirPath, folderName) {
    try {
      const fullPath = path.join(dirPath, folderName);
      
      if (!this.isPathAllowed(fullPath)) {
        throw new Error('Access denied to create folder in this location');
      }

      await fs.ensureDir(fullPath);
      return { success: true, path: fullPath };
    } catch (error) {
      console.error('Failed to create folder:', error);
      throw error;
    }
  }

  async deleteFiles(filePaths) {
    try {
      const results = [];
      
      for (const filePath of filePaths) {
        if (!this.isPathAllowed(filePath)) {
          results.push({ path: filePath, success: false, error: 'Access denied' });
          continue;
        }

        try {
          await fs.remove(filePath);
          results.push({ path: filePath, success: true });
        } catch (error) {
          results.push({ path: filePath, success: false, error: error.message });
        }
      }

      return results;
    } catch (error) {
      console.error('Failed to delete files:', error);
      throw error;
    }
  }

  async renameFile(oldPath, newPath) {
    try {
      if (!this.isPathAllowed(oldPath) || !this.isPathAllowed(newPath)) {
        throw new Error('Access denied');
      }

      await fs.move(oldPath, newPath);
      return { success: true, newPath };
    } catch (error) {
      console.error('Failed to rename file:', error);
      throw error;
    }
  }

  async copyFiles(sourcePaths, destinationDir) {
    try {
      if (!this.isPathAllowed(destinationDir)) {
        throw new Error('Access denied to destination directory');
      }

      const results = [];

      for (const sourcePath of sourcePaths) {
        if (!this.isPathAllowed(sourcePath)) {
          results.push({ source: sourcePath, success: false, error: 'Access denied to source' });
          continue;
        }

        try {
          const fileName = path.basename(sourcePath);
          const destPath = path.join(destinationDir, fileName);
          
          await fs.copy(sourcePath, destPath);
          results.push({ source: sourcePath, destination: destPath, success: true });
        } catch (error) {
          results.push({ source: sourcePath, success: false, error: error.message });
        }
      }

      return results;
    } catch (error) {
      console.error('Failed to copy files:', error);
      throw error;
    }
  }

  async getFileInfo(filePath) {
    try {
      if (!this.isPathAllowed(filePath)) {
        throw new Error('Access denied');
      }

      const stats = await fs.stat(filePath);
      const info = {
        path: filePath,
        name: path.basename(filePath),
        size: stats.size,
        isDirectory: stats.isDirectory(),
        modified: stats.mtime.toISOString(),
        created: stats.birthtime.toISOString(),
        accessed: stats.atime.toISOString(),
        permissions: this.getPermissions(stats),
        type: this.getFileType(path.basename(filePath), stats.isDirectory()),
        mimeType: stats.isDirectory() ? null : mime.lookup(filePath) || 'application/octet-stream'
      };

      return info;
    } catch (error) {
      console.error('Failed to get file info:', error);
      throw error;
    }
  }

  async searchFiles(searchDir, query, options = {}) {
    try {
      if (!this.isPathAllowed(searchDir)) {
        throw new Error('Access denied to search directory');
      }

      const {
        caseSensitive = false,
        includeHidden = false,
        fileTypesOnly = null, // array of file types to include
        maxResults = 1000
      } = options;

      const results = [];
      const searchQuery = caseSensitive ? query : query.toLowerCase();

      const searchRecursive = async (dir, depth = 0) => {
        if (depth > 10 || results.length >= maxResults) return; // Prevent infinite recursion

        try {
          const items = await fs.readdir(dir);

          for (const item of items) {
            if (results.length >= maxResults) break;

            // Skip hidden files if not included
            if (!includeHidden && item.startsWith('.')) continue;

            const itemPath = path.join(dir, item);
            const stats = await fs.stat(itemPath);
            
            const itemName = caseSensitive ? item : item.toLowerCase();
            if (itemName.includes(searchQuery)) {
              const fileType = this.getFileType(item, stats.isDirectory());
              
              // Filter by file type if specified
              if (fileTypesOnly && !fileTypesOnly.includes(fileType)) continue;

              results.push({
                name: item,
                path: itemPath,
                size: stats.size,
                isDirectory: stats.isDirectory(),
                modified: stats.mtime.toISOString(),
                type: fileType
              });
            }

            // Recurse into directories
            if (stats.isDirectory()) {
              await searchRecursive(itemPath, depth + 1);
            }
          }
        } catch (error) {
          // Skip directories that can't be accessed
          console.warn(`Cannot search in ${dir}:`, error.message);
        }
      };

      await searchRecursive(searchDir);
      return results;
    } catch (error) {
      console.error('Failed to search files:', error);
      throw error;
    }
  }

  getSystemDrives() {
    if (process.platform === 'win32') {
      // Windows: Get available drives
      const drives = [];
      for (let i = 65; i <= 90; i++) { // A-Z
        const drive = String.fromCharCode(i) + ':\\';
        try {
          if (fs.existsSync(drive)) {
            drives.push({
              name: drive,
              path: drive,
              type: 'drive',
              isDirectory: true
            });
          }
        } catch (error) {
          // Skip inaccessible drives
        }
      }
      return drives;
    } else {
      // Unix-like systems
      return [
        { name: 'Root', path: '/', type: 'drive', isDirectory: true },
        { name: 'Home', path: os.homedir(), type: 'drive', isDirectory: true }
      ];
    }
  }
}

module.exports = FileService;
