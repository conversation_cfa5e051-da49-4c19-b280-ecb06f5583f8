import React, { useState, useCallback, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ert,
  <PERSON>ert<PERSON><PERSON>le,
  Button,
  Box,
  Collapse,
  IconButton,
  Typography,
  Stack
} from '@mui/material';
import {
  Close as CloseIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { getErrorMessage } from '../utils/errorHandler';

const NotificationSystem = ({ maxNotifications = 5 }) => {
  const [notifications, setNotifications] = useState([]);
  const [expandedNotifications, setExpandedNotifications] = useState(new Set());

  const addNotification = useCallback((message, severity = 'info', options = {}) => {
    const id = Date.now() + Math.random();
    const notification = {
      id,
      message,
      severity,
      timestamp: new Date(),
      autoHideDuration: options.autoHideDuration ?? (severity === 'error' ? 8000 : 4000),
      persistent: options.persistent || false,
      error: options.error,
      context: options.context,
      suggestions: options.suggestions,
      retryAction: options.retryAction,
      ...options
    };

    setNotifications(prev => {
      const newNotifications = [notification, ...prev];
      return newNotifications.slice(0, maxNotifications);
    });

    // Auto-hide non-persistent notifications
    if (!notification.persistent && notification.autoHideDuration > 0) {
      setTimeout(() => {
        removeNotification(id);
      }, notification.autoHideDuration);
    }

    return id;
  }, [maxNotifications]);

  const removeNotification = useCallback((id) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
    setExpandedNotifications(prev => {
      const newSet = new Set(prev);
      newSet.delete(id);
      return newSet;
    });
  }, []);

  const toggleExpanded = useCallback((id) => {
    setExpandedNotifications(prev => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  }, []);

  const showError = useCallback((error, context = '', options = {}) => {
    const errorDetails = getErrorMessage(error, context);
    
    return addNotification(errorDetails.message, 'error', {
      ...options,
      error,
      context,
      title: errorDetails.title,
      suggestions: errorDetails.suggestions,
      persistent: true
    });
  }, [addNotification]);

  const showSuccess = useCallback((message, options = {}) => {
    return addNotification(message, 'success', options);
  }, [addNotification]);

  const showWarning = useCallback((message, options = {}) => {
    return addNotification(message, 'warning', options);
  }, [addNotification]);

  const showInfo = useCallback((message, options = {}) => {
    return addNotification(message, 'info', options);
  }, [addNotification]);

  const clearAll = useCallback(() => {
    setNotifications([]);
    setExpandedNotifications(new Set());
  }, []);

  // Expose methods globally for easy access
  useEffect(() => {
    window.notificationSystem = {
      showError,
      showSuccess,
      showWarning,
      showInfo,
      addNotification,
      removeNotification,
      clearAll
    };

    return () => {
      delete window.notificationSystem;
    };
  }, [showError, showSuccess, showWarning, showInfo, addNotification, removeNotification, clearAll]);

  const renderNotification = (notification) => {
    const isExpanded = expandedNotifications.has(notification.id);
    const hasDetails = notification.suggestions || notification.error || notification.context;

    return (
      <Snackbar
        key={notification.id}
        open={true}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
        sx={{
          position: 'relative',
          top: `${notifications.findIndex(n => n.id === notification.id) * 80}px`,
          zIndex: 1400 - notifications.findIndex(n => n.id === notification.id)
        }}
      >
        <Alert
          severity={notification.severity}
          variant="filled"
          sx={{
            minWidth: 350,
            maxWidth: 500,
            '& .MuiAlert-message': {
              width: '100%'
            }
          }}
          action={
            <Stack direction="row" spacing={1} alignItems="center">
              {notification.retryAction && (
                <IconButton
                  size="small"
                  color="inherit"
                  onClick={() => {
                    notification.retryAction();
                    removeNotification(notification.id);
                  }}
                  title="Retry"
                >
                  <RefreshIcon fontSize="small" />
                </IconButton>
              )}
              {hasDetails && (
                <IconButton
                  size="small"
                  color="inherit"
                  onClick={() => toggleExpanded(notification.id)}
                  title={isExpanded ? "Show less" : "Show more"}
                >
                  {isExpanded ? <ExpandLessIcon fontSize="small" /> : <ExpandMoreIcon fontSize="small" />}
                </IconButton>
              )}
              <IconButton
                size="small"
                color="inherit"
                onClick={() => removeNotification(notification.id)}
                title="Close"
              >
                <CloseIcon fontSize="small" />
              </IconButton>
            </Stack>
          }
        >
          <Box>
            {notification.title && (
              <AlertTitle sx={{ mb: 1 }}>
                {notification.title}
              </AlertTitle>
            )}
            
            <Typography variant="body2">
              {notification.message}
            </Typography>

            <Collapse in={isExpanded}>
              <Box sx={{ mt: 2 }}>
                {notification.suggestions && notification.suggestions.length > 0 && (
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" sx={{ mb: 1 }}>
                      Suggestions:
                    </Typography>
                    <ul style={{ margin: 0, paddingLeft: 20 }}>
                      {notification.suggestions.map((suggestion, index) => (
                        <li key={index}>
                          <Typography variant="body2">{suggestion}</Typography>
                        </li>
                      ))}
                    </ul>
                  </Box>
                )}

                {notification.context && (
                  <Box sx={{ mb: 1 }}>
                    <Typography variant="caption" color="text.secondary">
                      Context: {notification.context}
                    </Typography>
                  </Box>
                )}

                {notification.error && (
                  <Box sx={{ mb: 1 }}>
                    <Typography variant="caption" color="text.secondary">
                      Error: {notification.error.message || 'Unknown error'}
                    </Typography>
                  </Box>
                )}

                <Typography variant="caption" color="text.secondary">
                  {notification.timestamp.toLocaleTimeString()}
                </Typography>
              </Box>
            </Collapse>
          </Box>
        </Alert>
      </Snackbar>
    );
  };

  return (
    <Box>
      {notifications.map(renderNotification)}
      
      {notifications.length > 3 && (
        <Snackbar
          open={true}
          anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
          sx={{
            position: 'relative',
            top: `${notifications.length * 80}px`,
            zIndex: 1400 - notifications.length
          }}
        >
          <Alert
            severity="info"
            variant="outlined"
            action={
              <Button
                size="small"
                color="inherit"
                onClick={clearAll}
              >
                Clear All
              </Button>
            }
          >
            <Typography variant="body2">
              {notifications.length} notifications
            </Typography>
          </Alert>
        </Snackbar>
      )}
    </Box>
  );
};

// Hook for using the notification system
export const useNotifications = () => {
  const showNotification = useCallback((message, severity = 'info', options = {}) => {
    if (window.notificationSystem) {
      return window.notificationSystem.addNotification(message, severity, options);
    }
    console.warn('Notification system not available');
  }, []);

  const showError = useCallback((error, context = '', options = {}) => {
    if (window.notificationSystem) {
      return window.notificationSystem.showError(error, context, options);
    }
    console.error('Error notification:', error, context);
  }, []);

  const showSuccess = useCallback((message, options = {}) => {
    if (window.notificationSystem) {
      return window.notificationSystem.showSuccess(message, options);
    }
    console.log('Success:', message);
  }, []);

  const showWarning = useCallback((message, options = {}) => {
    if (window.notificationSystem) {
      return window.notificationSystem.showWarning(message, options);
    }
    console.warn('Warning:', message);
  }, []);

  const showInfo = useCallback((message, options = {}) => {
    if (window.notificationSystem) {
      return window.notificationSystem.showInfo(message, options);
    }
    console.info('Info:', message);
  }, []);

  return {
    showNotification,
    showError,
    showSuccess,
    showWarning,
    showInfo
  };
};

export default NotificationSystem;
