const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const os = require('os');

function getAdbPath() {
  const platform = os.platform();
  const resourcesPath = path.join(__dirname, '..', 'resources', 'adb');
  
  let adbBinaryPath;
  switch (platform) {
    case 'win32':
      adbBinaryPath = path.join(resourcesPath, 'win32', 'adb.exe');
      break;
    case 'darwin':
      adbBinaryPath = path.join(resourcesPath, 'darwin', 'adb');
      break;
    case 'linux':
      adbBinaryPath = path.join(resourcesPath, 'linux', 'adb');
      break;
    default:
      throw new Error(`Unsupported platform: ${platform}`);
  }
  
  return adbBinaryPath;
}

async function verifyAdb() {
  const adbPath = getAdbPath();
  
  console.log(`Verifying ADB binary at: ${adbPath}`);
  
  // Check if file exists
  if (!fs.existsSync(adbPath)) {
    console.error('❌ ADB binary not found!');
    console.log('Run "npm run download-adb" to download ADB binaries.');
    process.exit(1);
  }
  
  // Check if file is executable (Unix systems)
  if (os.platform() !== 'win32') {
    try {
      const stats = fs.statSync(adbPath);
      if (!(stats.mode & parseInt('111', 8))) {
        console.log('Making ADB binary executable...');
        fs.chmodSync(adbPath, 0o755);
      }
    } catch (error) {
      console.error('❌ Failed to check/set ADB permissions:', error.message);
      process.exit(1);
    }
  }
  
  // Test ADB version command
  return new Promise((resolve, reject) => {
    console.log('Testing ADB version command...');
    
    const adb = spawn(adbPath, ['version'], {
      stdio: ['pipe', 'pipe', 'pipe']
    });
    
    let stdout = '';
    let stderr = '';
    
    adb.stdout.on('data', (data) => {
      stdout += data.toString();
    });
    
    adb.stderr.on('data', (data) => {
      stderr += data.toString();
    });
    
    adb.on('close', (code) => {
      if (code === 0) {
        console.log('✅ ADB binary is working correctly!');
        console.log('ADB Version:', stdout.trim().split('\n')[0]);
        resolve();
      } else {
        console.error('❌ ADB binary test failed!');
        console.error('Exit code:', code);
        console.error('Error output:', stderr);
        reject(new Error(`ADB test failed with exit code ${code}`));
      }
    });
    
    adb.on('error', (error) => {
      console.error('❌ Failed to execute ADB binary:', error.message);
      reject(error);
    });
  });
}

// Run verification if called directly
if (require.main === module) {
  verifyAdb()
    .then(() => {
      console.log('\n🎉 ADB verification completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 ADB verification failed:', error.message);
      process.exit(1);
    });
}

module.exports = { verifyAdb };
