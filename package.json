{"name": "android-manager", "version": "1.0.0", "description": "Advanced Android device management desktop application", "main": "src/main/main.js", "homepage": "./", "scripts": {"start": "electron .", "dev": "concurrently \"npm run dev:renderer\" \"wait-on http://localhost:3000 && electron .\"", "dev:renderer": "react-scripts start", "build": "react-scripts build", "build:electron": "npm run download-adb && npm run build && electron-builder", "build:win": "npm run download-adb && npm run build && electron-builder --win", "build:mac": "npm run download-adb && npm run build && electron-builder --mac", "build:linux": "npm run download-adb && npm run build && electron-builder --linux", "test": "react-scripts test", "eject": "react-scripts eject", "download-adb": "node scripts/download-adb.js", "verify-adb": "node scripts/verify-adb.js"}, "keywords": ["android", "device-manager", "adb", "file-transfer", "electron", "desktop-app"], "author": "Android Manager Team", "license": "MIT", "dependencies": {"@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.0", "@mui/material": "^5.14.0", "chokidar": "^3.5.3", "electron": "^27.0.0", "electron-is-dev": "^2.0.0", "fs-extra": "^11.1.1", "lodash": "^4.17.21", "mime-types": "^2.1.35", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.15.0", "react-scripts": "5.0.1", "uuid": "^9.0.0"}, "devDependencies": {"@types/node": "^20.5.0", "concurrently": "^8.2.0", "electron-builder": "^24.6.0", "typescript": "^4.9.5", "wait-on": "^7.0.1"}, "build": {"appId": "com.androidmanager.app", "productName": "Android Manager", "directories": {"output": "dist"}, "files": ["build/**/*", "src/main/**/*", "resources/**/*", "node_modules/**/*", "package.json"], "mac": {"category": "public.app-category.utilities", "target": "dmg"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}