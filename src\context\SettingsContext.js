import React, { createContext, useContext, useReducer, useEffect } from 'react';

// Initial state
const initialState = {
  settings: {
    theme: 'light',
    language: 'en',
    maxConcurrentTransfers: 3,
    autoConnect: true,
    showHiddenFiles: false,
    defaultDownloadPath: '',
    notifications: true,
    autoBackup: false,
    compressionLevel: 6,
    transferChunkSize: 1024 * 1024, // 1MB
    keepTransferHistory: 30, // days
    autoRefreshInterval: 10, // seconds
    soundNotifications: true,
    minimizeToTray: true,
    startMinimized: false,
    checkUpdates: true
  },
  loading: false,
  error: null,
  saving: false
};

// Action types
const ActionTypes = {
  SET_LOADING: 'SET_LOADING',
  SET_SAVING: 'SET_SAVING',
  SET_SETTINGS: 'SET_SETTINGS',
  UPDATE_SETTING: 'UPDATE_SETTING',
  SET_ERROR: 'SET_ERROR',
  CLEAR_ERROR: 'CLEAR_ERROR'
};

// Reducer
function settingsReducer(state, action) {
  switch (action.type) {
    case ActionTypes.SET_LOADING:
      return { ...state, loading: action.payload };
    
    case ActionTypes.SET_SAVING:
      return { ...state, saving: action.payload };
    
    case ActionTypes.SET_SETTINGS:
      return { 
        ...state, 
        settings: { ...state.settings, ...action.payload },
        loading: false 
      };
    
    case ActionTypes.UPDATE_SETTING:
      return {
        ...state,
        settings: {
          ...state.settings,
          [action.payload.key]: action.payload.value
        }
      };
    
    case ActionTypes.SET_ERROR:
      return { ...state, error: action.payload, loading: false, saving: false };
    
    case ActionTypes.CLEAR_ERROR:
      return { ...state, error: null };
    
    default:
      return state;
  }
}

// Context
const SettingsContext = createContext();

// Provider component
export function SettingsProvider({ children }) {
  const [state, dispatch] = useReducer(settingsReducer, initialState);

  // Actions
  const actions = {
    setLoading: (loading) => {
      dispatch({ type: ActionTypes.SET_LOADING, payload: loading });
    },

    setSaving: (saving) => {
      dispatch({ type: ActionTypes.SET_SAVING, payload: saving });
    },

    setError: (error) => {
      dispatch({ type: ActionTypes.SET_ERROR, payload: error });
    },

    clearError: () => {
      dispatch({ type: ActionTypes.CLEAR_ERROR });
    },

    loadSettings: async () => {
      try {
        if (!window.electronAPI?.settings?.get) {
          console.warn('Settings API not available - running in development mode');
          return;
        }
        dispatch({ type: ActionTypes.SET_LOADING, payload: true });
        const settings = await window.electronAPI.settings.get();
        dispatch({ type: ActionTypes.SET_SETTINGS, payload: settings });
      } catch (error) {
        console.error('Failed to load settings:', error);
        dispatch({ type: ActionTypes.SET_ERROR, payload: error.message });
      } finally {
        dispatch({ type: ActionTypes.SET_LOADING, payload: false });
      }
    },

    saveSettings: async (settings) => {
      try {
        if (!window.electronAPI?.settings?.set) {
          console.warn('Settings API not available - running in development mode');
          return;
        }
        dispatch({ type: ActionTypes.SET_SAVING, payload: true });
        await window.electronAPI.settings.set(settings);
        dispatch({ type: ActionTypes.SET_SETTINGS, payload: settings });
        dispatch({ type: ActionTypes.SET_SAVING, payload: false });
      } catch (error) {
        console.error('Failed to save settings:', error);
        dispatch({ type: ActionTypes.SET_ERROR, payload: error.message });
        throw error;
      }
    },

    updateSetting: async (key, value) => {
      try {
        const newSettings = { ...state.settings, [key]: value };
        await actions.saveSettings(newSettings);
        dispatch({ type: ActionTypes.UPDATE_SETTING, payload: { key, value } });
      } catch (error) {
        console.error('Failed to update setting:', error);
        dispatch({ type: ActionTypes.SET_ERROR, payload: error.message });
        throw error;
      }
    },

    resetSettings: async () => {
      try {
        const defaultSettings = {
          theme: 'light',
          language: 'en',
          maxConcurrentTransfers: 3,
          autoConnect: true,
          showHiddenFiles: false,
          defaultDownloadPath: '',
          notifications: true,
          autoBackup: false,
          compressionLevel: 6,
          transferChunkSize: 1024 * 1024,
          keepTransferHistory: 30,
          autoRefreshInterval: 10,
          soundNotifications: true,
          minimizeToTray: true,
          startMinimized: false,
          checkUpdates: true
        };
        
        await actions.saveSettings(defaultSettings);
      } catch (error) {
        console.error('Failed to reset settings:', error);
        dispatch({ type: ActionTypes.SET_ERROR, payload: error.message });
        throw error;
      }
    },

    exportSettings: () => {
      try {
        const settingsJson = JSON.stringify(state.settings, null, 2);
        const blob = new Blob([settingsJson], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = 'android-manager-settings.json';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      } catch (error) {
        console.error('Failed to export settings:', error);
        dispatch({ type: ActionTypes.SET_ERROR, payload: error.message });
        throw error;
      }
    },

    importSettings: async (file) => {
      try {
        const text = await file.text();
        const importedSettings = JSON.parse(text);
        
        // Validate imported settings
        const validatedSettings = { ...state.settings };
        
        Object.keys(importedSettings).forEach(key => {
          if (key in validatedSettings) {
            validatedSettings[key] = importedSettings[key];
          }
        });
        
        await actions.saveSettings(validatedSettings);
      } catch (error) {
        console.error('Failed to import settings:', error);
        dispatch({ type: ActionTypes.SET_ERROR, payload: error.message });
        throw error;
      }
    }
  };

  // Load settings on mount
  useEffect(() => {
    // Check if electronAPI is available
    if (!window.electronAPI?.settings) {
      console.warn('Settings API not available - running in development mode');
      return;
    }
    actions.loadSettings();
  }, []);

  const value = {
    ...state,
    ...actions
  };

  return (
    <SettingsContext.Provider value={value}>
      {children}
    </SettingsContext.Provider>
  );
}

// Hook to use settings context
export function useSettings() {
  const context = useContext(SettingsContext);
  if (!context) {
    throw new Error('useSettings must be used within a SettingsProvider');
  }
  return context;
}

export default SettingsContext;
