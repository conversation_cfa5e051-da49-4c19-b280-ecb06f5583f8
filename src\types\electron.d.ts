// Type declarations for Electron API exposed through preload script

interface ElectronAPI {
  device: {
    list: () => Promise<any[]>;
    connect: (deviceId: string) => Promise<any>;
    disconnect: (deviceId: string) => Promise<any>;
    getInfo: (deviceId: string) => Promise<any>;
    getDetailedInfo: (deviceId: string) => Promise<any>;
    getAdbStatus: () => Promise<any>;
    restartAdb: () => Promise<any>;
    takeScreenshot: (deviceId: string) => Promise<any>;
    saveScreenshot: (imagePath: string) => Promise<any>;
    startLogcat: (deviceId: string, options: any) => Promise<any>;
    stopLogcat: (deviceId: string) => Promise<any>;
    saveLogcat: (logs: any[]) => Promise<any>;
    executeShell: (deviceId: string, command: string) => Promise<any>;
    reboot: (deviceId: string, mode: string) => Promise<any>;
    toggleWifi: (deviceId: string) => Promise<any>;
    onLogcatData?: (callback: (data: any) => void) => (() => void);
  };
  
  file: {
    list: (deviceId: string, path: string) => Promise<any[]>;
    listPC: (path: string) => Promise<any[]>;
    createFolder: (deviceId: string, path: string, name: string) => Promise<any>;
    delete: (deviceId: string, paths: string[]) => Promise<any>;
    rename: (deviceId: string, oldPath: string, newPath: string) => Promise<any>;
    createFolderPC: (dirPath: string, folderName: string) => Promise<any>;
    deletePC: (filePaths: string[]) => Promise<any>;
    renamePC: (oldPath: string, newPath: string) => Promise<any>;
    copyPC: (sourcePaths: string[], destinationDir: string) => Promise<any>;
    getInfo: (filePath: string) => Promise<any>;
    search: (searchDir: string, query: string, options: any) => Promise<any>;
    getDrives: () => Promise<any[]>;
    getPlatformInfo: () => Promise<{ platform: string; separator: string }>;
  };
  
  transfer: {
    upload: (deviceId: string, localPaths: string[], remotePath: string) => Promise<any>;
    download: (deviceId: string, remotePaths: string[], localPath: string) => Promise<any>;
    onProgress?: (callback: (data: any) => void) => (() => void);
    onComplete?: (callback: (data: any) => void) => (() => void);
  };
  
  app: {
    list: (deviceId: string) => Promise<any[]>;
    install: (deviceId: string, apkPath: string) => Promise<any>;
    uninstall: (deviceId: string, packageName: string) => Promise<any>;
    launch: (deviceId: string, packageName: string) => Promise<any>;
    stop: (deviceId: string, packageName: string) => Promise<any>;
    clear: (deviceId: string, packageName: string) => Promise<any>;
    getInfo: (deviceId: string, packageName: string) => Promise<any>;
  };
  
  dialog: {
    openFile: (options: any) => Promise<any>;
    openDirectory: (options: any) => Promise<any>;
    saveFile: (options: any) => Promise<any>;
  };
  
  settings: {
    get: (key: string) => Promise<any>;
    set: (key: string, value: any) => Promise<any>;
    getAll: () => Promise<any>;
  };
  
  system: {
    getInfo: () => Promise<any>;
    openExternal: (url: string) => Promise<void>;
    showInFolder: (path: string) => Promise<void>;
  };
}

declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}

export {};
