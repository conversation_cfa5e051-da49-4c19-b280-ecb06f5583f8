import React, { useState, useEffect } from 'react';
import {
  TextField,
  FormHelperText,
  InputAdornment,
  IconButton
} from '@mui/material';
import {
  CheckCircle as CheckIcon,
  Error as ErrorIcon,
  Clear as ClearIcon
} from '@mui/icons-material';
import { validateInput } from '../utils/errorHandler';

const ValidatedTextField = ({
  value,
  onChange,
  onValidationChange,
  validationType,
  customValidator,
  required = false,
  showValidationIcon = true,
  clearable = false,
  debounceMs = 300,
  ...textFieldProps
}) => {
  const [error, setError] = useState('');
  const [isValid, setIsValid] = useState(true);
  const [touched, setTouched] = useState(false);

  useEffect(() => {
    if (!touched && !value) return;

    const timeoutId = setTimeout(() => {
      validateValue(value);
    }, debounceMs);

    return () => clearTimeout(timeoutId);
  }, [value, validationType, customValidator, required, touched]);

  const validateValue = (val) => {
    try {
      let errorMessage = '';

      // Required validation
      if (required && (!val || val.trim() === '')) {
        errorMessage = `${textFieldProps.label || 'Field'} is required`;
      }
      
      // Skip other validations if empty and not required
      if (!val || val.trim() === '') {
        if (!required) {
          setError('');
          setIsValid(true);
          if (onValidationChange) {
            onValidationChange(true, '');
          }
          return;
        }
      }

      // Built-in validation types
      if (validationType && val) {
        switch (validationType) {
          case 'fileName':
            validateInput.fileName(val);
            break;
          case 'path':
            validateInput.path(val);
            break;
          case 'packageName':
            validateInput.packageName(val);
            break;
          case 'deviceId':
            validateInput.deviceId(val);
            break;
          case 'required':
            validateInput.required(val, textFieldProps.label || 'Field');
            break;
          default:
            break;
        }
      }

      // Custom validator
      if (customValidator && val) {
        const customResult = customValidator(val);
        if (customResult !== true) {
          errorMessage = customResult || 'Invalid value';
        }
      }

      setError(errorMessage);
      setIsValid(!errorMessage);
      
      if (onValidationChange) {
        onValidationChange(!errorMessage, errorMessage);
      }
    } catch (validationError) {
      const errorMessage = validationError.message || 'Invalid value';
      setError(errorMessage);
      setIsValid(false);
      
      if (onValidationChange) {
        onValidationChange(false, errorMessage);
      }
    }
  };

  const handleChange = (event) => {
    const newValue = event.target.value;
    setTouched(true);
    
    if (onChange) {
      onChange(event);
    }
  };

  const handleBlur = (event) => {
    setTouched(true);
    validateValue(event.target.value);
    
    if (textFieldProps.onBlur) {
      textFieldProps.onBlur(event);
    }
  };

  const handleClear = () => {
    const event = {
      target: { value: '' }
    };
    
    if (onChange) {
      onChange(event);
    }
    
    setTouched(true);
    validateValue('');
  };

  const getValidationIcon = () => {
    if (!showValidationIcon || !touched) return null;
    
    if (error) {
      return <ErrorIcon color="error" />;
    } else if (value && isValid) {
      return <CheckIcon color="success" />;
    }
    
    return null;
  };

  const getEndAdornment = () => {
    const validationIcon = getValidationIcon();
    const clearButton = clearable && value && (
      <IconButton
        size="small"
        onClick={handleClear}
        edge="end"
      >
        <ClearIcon />
      </IconButton>
    );

    if (validationIcon || clearButton) {
      return (
        <InputAdornment position="end">
          {clearButton}
          {validationIcon}
        </InputAdornment>
      );
    }

    return textFieldProps.InputProps?.endAdornment;
  };

  return (
    <>
      <TextField
        {...textFieldProps}
        value={value}
        onChange={handleChange}
        onBlur={handleBlur}
        error={touched && !!error}
        InputProps={{
          ...textFieldProps.InputProps,
          endAdornment: getEndAdornment()
        }}
      />
      {touched && error && (
        <FormHelperText error>
          {error}
        </FormHelperText>
      )}
    </>
  );
};

// Predefined validation types for common use cases
export const ValidationTypes = {
  FILE_NAME: 'fileName',
  PATH: 'path',
  PACKAGE_NAME: 'packageName',
  DEVICE_ID: 'deviceId',
  REQUIRED: 'required'
};

// Common custom validators
export const CustomValidators = {
  minLength: (min) => (value) => {
    if (value && value.length < min) {
      return `Must be at least ${min} characters long`;
    }
    return true;
  },
  
  maxLength: (max) => (value) => {
    if (value && value.length > max) {
      return `Must be no more than ${max} characters long`;
    }
    return true;
  },
  
  pattern: (regex, message) => (value) => {
    if (value && !regex.test(value)) {
      return message || 'Invalid format';
    }
    return true;
  },
  
  noSpaces: (value) => {
    if (value && value.includes(' ')) {
      return 'Spaces are not allowed';
    }
    return true;
  },
  
  alphanumeric: (value) => {
    if (value && !/^[a-zA-Z0-9]+$/.test(value)) {
      return 'Only letters and numbers are allowed';
    }
    return true;
  },
  
  email: (value) => {
    if (value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
      return 'Please enter a valid email address';
    }
    return true;
  },
  
  url: (value) => {
    if (value) {
      try {
        new URL(value);
        return true;
      } catch {
        return 'Please enter a valid URL';
      }
    }
    return true;
  },
  
  combine: (...validators) => (value) => {
    for (const validator of validators) {
      const result = validator(value);
      if (result !== true) {
        return result;
      }
    }
    return true;
  }
};

export default ValidatedTextField;
