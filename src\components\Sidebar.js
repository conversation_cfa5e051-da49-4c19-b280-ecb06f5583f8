import React from 'react';
import {
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Divider,
  Typography,
  Box,
  Chip,
  Avatar,
  Tooltip
} from '@mui/material';
import {
  Smartphone,
  Folder,
  SwapHoriz,
  Apps,
  Build,
  Settings,
  Android,
  Wifi,
  WifiOff,
  Battery3Bar,
  Info
} from '@mui/icons-material';

const DRAWER_WIDTH = 280;

const menuItems = [
  { id: 'devices', label: 'Devices', icon: Smartphone },
  { id: 'files', label: 'File Browser', icon: Folder },
  { id: 'transfers', label: 'Transfers', icon: SwapHoriz },
  { id: 'applications', label: 'Applications', icon: Apps },
  { id: 'tools', label: 'Device Tools', icon: Build },
  { id: 'settings', label: 'Settings', icon: Settings }
];

function Sidebar({ currentView, onViewChange, selectedDevice, theme }) {
  const handleMenuClick = (viewId) => {
    onViewChange(viewId);
  };

  const renderDeviceInfo = () => {
    if (!selectedDevice) {
      return (
        <Box sx={{ p: 2, textAlign: 'center' }}>
          <Typography variant="body2" color="text.secondary">
            No device selected
          </Typography>
        </Box>
      );
    }

    const isConnected = selectedDevice.status === 'device';

    return (
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <Avatar sx={{ bgcolor: isConnected ? 'success.main' : 'error.main', mr: 1 }}>
            <Android />
          </Avatar>
          <Box sx={{ flexGrow: 1, minWidth: 0 }}>
            <Typography variant="subtitle2" noWrap>
              {selectedDevice.model || selectedDevice.id}
            </Typography>
            <Typography variant="caption" color="text.secondary" noWrap>
              {selectedDevice.manufacturer || 'Unknown'}
            </Typography>
          </Box>
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
          <Chip
            size="small"
            icon={isConnected ? <Wifi /> : <WifiOff />}
            label={isConnected ? 'Connected' : 'Disconnected'}
            color={isConnected ? 'success' : 'error'}
            variant="outlined"
          />
        </Box>

        {selectedDevice.androidVersion && (
          <Typography variant="caption" color="text.secondary" display="block">
            Android {selectedDevice.androidVersion}
          </Typography>
        )}

        {selectedDevice.batteryLevel && (
          <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
            <Battery3Bar fontSize="small" color="action" />
            <Typography variant="caption" color="text.secondary" sx={{ ml: 0.5 }}>
              {selectedDevice.batteryLevel}
            </Typography>
          </Box>
        )}
      </Box>
    );
  };

  const isMenuItemDisabled = (itemId) => {
    // Disable certain menu items when no device is selected
    const deviceRequiredItems = ['files', 'applications', 'tools'];
    return deviceRequiredItems.includes(itemId) && !selectedDevice;
  };

  return (
    <Drawer
      variant="permanent"
      sx={{
        width: DRAWER_WIDTH,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: DRAWER_WIDTH,
          boxSizing: 'border-box',
          borderRight: 1,
          borderColor: 'divider'
        },
      }}
    >
      {/* App Header */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Android sx={{ color: 'primary.main', mr: 1 }} />
          <Typography variant="h6" component="div" sx={{ fontWeight: 600 }}>
            Android Manager
          </Typography>
        </Box>
        <Typography variant="caption" color="text.secondary">
          Device Management Tool
        </Typography>
      </Box>

      {/* Device Info */}
      {renderDeviceInfo()}

      {/* Navigation Menu */}
      <List sx={{ flexGrow: 1, py: 1 }}>
        {menuItems.map((item) => {
          const Icon = item.icon;
          const isSelected = currentView === item.id;
          const isDisabled = isMenuItemDisabled(item.id);

          return (
            <Tooltip
              key={item.id}
              title={isDisabled ? 'Select a device first' : ''}
              placement="right"
            >
              <ListItem disablePadding>
                <ListItemButton
                  selected={isSelected}
                  disabled={isDisabled}
                  onClick={() => handleMenuClick(item.id)}
                  sx={{
                    mx: 1,
                    borderRadius: 1,
                    '&.Mui-selected': {
                      backgroundColor: 'primary.main',
                      color: 'primary.contrastText',
                      '&:hover': {
                        backgroundColor: 'primary.dark',
                      },
                      '& .MuiListItemIcon-root': {
                        color: 'primary.contrastText',
                      },
                    },
                  }}
                >
                  <ListItemIcon>
                    <Icon />
                  </ListItemIcon>
                  <ListItemText 
                    primary={item.label}
                    primaryTypographyProps={{
                      fontSize: '0.875rem',
                      fontWeight: isSelected ? 600 : 400
                    }}
                  />
                </ListItemButton>
              </ListItem>
            </Tooltip>
          );
        })}
      </List>

      <Divider />

      {/* Footer */}
      <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider' }}>
        <Typography variant="caption" color="text.secondary" align="center" display="block">
          Version 1.0.0
        </Typography>
        <Typography variant="caption" color="text.secondary" align="center" display="block">
          © 2024 Android Manager
        </Typography>
      </Box>
    </Drawer>
  );
}

export default Sidebar;
