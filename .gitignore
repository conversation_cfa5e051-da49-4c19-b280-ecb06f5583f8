# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# production
/build
/dist

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Electron
/out/
/dist_electron/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
Thumbs.db

# Temporary files
/temp/
/tmp/

# ADB binaries (downloaded automatically)
resources/adb/win32/adb.exe
resources/adb/win32/AdbWinApi.dll
resources/adb/win32/AdbWinUsbApi.dll
resources/adb/darwin/adb
resources/adb/linux/adb

# Database files
*.db
*.sqlite
*.sqlite3

# Logs
logs/
*.log
