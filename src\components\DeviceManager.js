import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typography,
  Card,
  CardContent,
  CardActions,
  Button,
  Grid,
  Chip,
  Avatar,
  IconButton,
  Tooltip,
  CircularProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  Divider
} from '@mui/material';
import {
  Android,
  Wifi,
  WifiOff,
  Info,
  Refresh,
  Settings,
  Battery3Bar,
  Memory,
  Storage,
  Smartphone
} from '@mui/icons-material';
import { useDevice } from '../context/DeviceContext';

function DeviceManager({ onDeviceSelect, selectedDevice }) {
  const {
    devices,
    connectedDevices,
    deviceInfo,
    loading,
    refreshing,
    error,
    refreshDevices,
    selectDevice,
    connectDevice,
    disconnectDevice,
    getDeviceInfo
  } = useDevice();

  const [detailsDialog, setDetailsDialog] = useState({ open: false, device: null });
  const [connectingDevice, setConnectingDevice] = useState(null);

  useEffect(() => {
    // Auto-refresh devices every 10 seconds
    const interval = setInterval(() => {
      if (!loading && !refreshing) {
        refreshDevices();
      }
    }, 10000);

    return () => clearInterval(interval);
  }, [loading, refreshing, refreshDevices]);

  const handleDeviceSelect = async (device) => {
    try {
      await selectDevice(device);
      onDeviceSelect(device);
    } catch (error) {
      console.error('Failed to select device:', error);
    }
  };

  const handleConnect = async (device) => {
    try {
      setConnectingDevice(device.id);
      await connectDevice(device.id);
      await refreshDevices();
    } catch (error) {
      console.error('Failed to connect device:', error);
    } finally {
      setConnectingDevice(null);
    }
  };

  const handleDisconnect = async (device) => {
    try {
      setConnectingDevice(device.id);
      await disconnectDevice(device.id);
      await refreshDevices();
    } catch (error) {
      console.error('Failed to disconnect device:', error);
    } finally {
      setConnectingDevice(null);
    }
  };

  const handleShowDetails = async (device) => {
    try {
      const info = await getDeviceInfo(device.id);
      setDetailsDialog({ open: true, device: { ...device, ...info } });
    } catch (error) {
      console.error('Failed to get device details:', error);
    }
  };

  const handleCloseDetails = () => {
    setDetailsDialog({ open: false, device: null });
  };

  const renderDeviceCard = (device) => {
    const isConnected = device.status === 'device';
    const isSelected = selectedDevice && selectedDevice.id === device.id;
    const isConnecting = connectingDevice === device.id;
    const info = deviceInfo[device.id];

    return (
      <Grid item xs={12} sm={6} md={4} key={device.id}>
        <Card
          sx={{
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            border: isSelected ? 2 : 1,
            borderColor: isSelected ? 'primary.main' : 'divider',
            cursor: 'pointer',
            transition: 'all 0.2s',
            '&:hover': {
              boxShadow: 3,
              transform: 'translateY(-2px)'
            }
          }}
          onClick={() => handleDeviceSelect(device)}
        >
          <CardContent sx={{ flexGrow: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Avatar
                sx={{
                  bgcolor: isConnected ? 'success.main' : 'error.main',
                  mr: 2
                }}
              >
                <Android />
              </Avatar>
              <Box sx={{ flexGrow: 1, minWidth: 0 }}>
                <Typography variant="h6" noWrap>
                  {device.model || device.product || device.id}
                </Typography>
                <Typography variant="body2" color="text.secondary" noWrap>
                  {device.device || 'Unknown Device'}
                </Typography>
              </Box>
              <Chip
                size="small"
                icon={isConnected ? <Wifi /> : <WifiOff />}
                label={isConnected ? 'Connected' : 'Disconnected'}
                color={isConnected ? 'success' : 'error'}
                variant="outlined"
              />
            </Box>

            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" color="text.secondary">
                Device ID: {device.id}
              </Typography>
              {info && (
                <>
                  <Typography variant="body2" color="text.secondary">
                    Manufacturer: {info.manufacturer}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Android: {info.androidVersion}
                  </Typography>
                  {info.batteryLevel && (
                    <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                      <Battery3Bar fontSize="small" color="action" />
                      <Typography variant="body2" color="text.secondary" sx={{ ml: 0.5 }}>
                        Battery: {info.batteryLevel}
                      </Typography>
                    </Box>
                  )}
                </>
              )}
            </Box>

            {device.status === 'unauthorized' && (
              <Alert severity="warning" size="small">
                Device unauthorized. Please allow USB debugging on your device.
              </Alert>
            )}
          </CardContent>

          <CardActions sx={{ justifyContent: 'space-between', px: 2, pb: 2 }}>
            <Box>
              {isConnected ? (
                <Button
                  size="small"
                  color="error"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDisconnect(device);
                  }}
                  disabled={isConnecting}
                  startIcon={isConnecting ? <CircularProgress size={16} /> : <WifiOff />}
                >
                  Disconnect
                </Button>
              ) : (
                <Button
                  size="small"
                  color="primary"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleConnect(device);
                  }}
                  disabled={isConnecting || device.status === 'unauthorized'}
                  startIcon={isConnecting ? <CircularProgress size={16} /> : <Wifi />}
                >
                  Connect
                </Button>
              )}
            </Box>

            <Box>
              <Tooltip title="Device Details">
                <IconButton
                  size="small"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleShowDetails(device);
                  }}
                >
                  <Info />
                </IconButton>
              </Tooltip>
            </Box>
          </CardActions>
        </Card>
      </Grid>
    );
  };

  return (
    <Box sx={{ p: 3, height: '100%', overflow: 'auto' }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            Device Manager
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage your Android devices and connections
          </Typography>
        </Box>
        <Button
          variant="outlined"
          startIcon={refreshing ? <CircularProgress size={20} /> : <Refresh />}
          onClick={refreshDevices}
          disabled={refreshing}
        >
          Refresh
        </Button>
      </Box>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Statistics */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={4}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Smartphone sx={{ color: 'primary.main', mr: 2 }} />
                <Box>
                  <Typography variant="h6">{devices.length}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Devices
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={4}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Wifi sx={{ color: 'success.main', mr: 2 }} />
                <Box>
                  <Typography variant="h6">{connectedDevices.length}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Connected
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={4}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <WifiOff sx={{ color: 'error.main', mr: 2 }} />
                <Box>
                  <Typography variant="h6">{devices.length - connectedDevices.length}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Disconnected
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Device Grid */}
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
          <CircularProgress />
        </Box>
      ) : devices.length === 0 ? (
        <Card>
          <CardContent sx={{ textAlign: 'center', py: 4 }}>
            <Android sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" gutterBottom>
              No devices found
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Make sure your Android device is connected via USB and USB debugging is enabled.
            </Typography>
            <Button variant="outlined" onClick={refreshDevices} startIcon={<Refresh />}>
              Refresh Devices
            </Button>
          </CardContent>
        </Card>
      ) : (
        <Grid container spacing={2}>
          {devices.map(renderDeviceCard)}
        </Grid>
      )}

      {/* Device Details Dialog */}
      <Dialog
        open={detailsDialog.open}
        onClose={handleCloseDetails}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          Device Details
        </DialogTitle>
        <DialogContent>
          {detailsDialog.device && (
            <List>
              <ListItem>
                <ListItemText
                  primary="Device ID"
                  secondary={detailsDialog.device.id}
                />
              </ListItem>
              <Divider />
              <ListItem>
                <ListItemText
                  primary="Model"
                  secondary={detailsDialog.device.model || 'Unknown'}
                />
              </ListItem>
              <Divider />
              <ListItem>
                <ListItemText
                  primary="Manufacturer"
                  secondary={detailsDialog.device.manufacturer || 'Unknown'}
                />
              </ListItem>
              <Divider />
              <ListItem>
                <ListItemText
                  primary="Android Version"
                  secondary={detailsDialog.device.androidVersion || 'Unknown'}
                />
              </ListItem>
              <Divider />
              <ListItem>
                <ListItemText
                  primary="SDK Version"
                  secondary={detailsDialog.device.sdkVersion || 'Unknown'}
                />
              </ListItem>
              <Divider />
              <ListItem>
                <ListItemText
                  primary="Serial Number"
                  secondary={detailsDialog.device.serial || 'Unknown'}
                />
              </ListItem>
              <Divider />
              <ListItem>
                <ListItemText
                  primary="Battery Level"
                  secondary={detailsDialog.device.batteryLevel || 'Unknown'}
                />
              </ListItem>
              <Divider />
              <ListItem>
                <ListItemText
                  primary="Status"
                  secondary={detailsDialog.device.status}
                />
              </ListItem>
            </List>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDetails}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default DeviceManager;
