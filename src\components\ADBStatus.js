import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Chip,
  Alert,
  Button,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  Divider
} from '@mui/material';
import {
  CheckCircle,
  Error,
  Info,
  Refresh,
  Build
} from '@mui/icons-material';

function ADBStatus({ onShowNotification }) {
  const [adbStatus, setAdbStatus] = useState({
    isAvailable: false,
    version: null,
    path: null,
    error: null,
    loading: true
  });
  const [detailsOpen, setDetailsOpen] = useState(false);

  useEffect(() => {
    checkAdbStatus();
  }, []);

  const checkAdbStatus = async () => {
    try {
      setAdbStatus(prev => ({ ...prev, loading: true }));
      
      const status = await window.electronAPI.device.getAdbStatus();
      setAdbStatus({
        isAvailable: status.available,
        version: status.version,
        path: status.path,
        error: status.error,
        loading: false
      });
    } catch (error) {
      console.error('Failed to check ADB status:', error);
      setAdbStatus({
        isAvailable: false,
        version: null,
        path: null,
        error: error.message,
        loading: false
      });
    }
  };

  const handleRestartAdb = async () => {
    try {
      onShowNotification('Restarting ADB server...', 'info');
      await window.electronAPI.device.restartAdb();
      await checkAdbStatus();
      onShowNotification('ADB server restarted successfully', 'success');
    } catch (error) {
      console.error('Failed to restart ADB:', error);
      onShowNotification('Failed to restart ADB server', 'error');
    }
  };

  const renderStatusChip = () => {
    if (adbStatus.loading) {
      return (
        <Chip
          icon={<CircularProgress size={16} />}
          label="Checking..."
          variant="outlined"
        />
      );
    }

    if (adbStatus.isAvailable) {
      return (
        <Chip
          icon={<CheckCircle />}
          label="ADB Ready"
          color="success"
          variant="outlined"
        />
      );
    }

    return (
      <Chip
        icon={<Error />}
        label="ADB Error"
        color="error"
        variant="outlined"
      />
    );
  };

  const renderStatusMessage = () => {
    if (adbStatus.loading) {
      return (
        <Typography variant="body2" color="text.secondary">
          Checking ADB status...
        </Typography>
      );
    }

    if (adbStatus.isAvailable) {
      return (
        <Typography variant="body2" color="text.secondary">
          Android Debug Bridge is ready for device communication
        </Typography>
      );
    }

    return (
      <Alert severity="error" sx={{ mt: 1 }}>
        <Typography variant="body2">
          ADB is not available: {adbStatus.error}
        </Typography>
      </Alert>
    );
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
        <Build color="action" />
        <Typography variant="subtitle2">ADB Status</Typography>
        {renderStatusChip()}
      </Box>

      {renderStatusMessage()}

      <Box sx={{ display: 'flex', gap: 1, mt: 2 }}>
        <Button
          size="small"
          variant="outlined"
          startIcon={<Refresh />}
          onClick={checkAdbStatus}
          disabled={adbStatus.loading}
        >
          Refresh
        </Button>
        
        {adbStatus.isAvailable && (
          <Button
            size="small"
            variant="outlined"
            onClick={handleRestartAdb}
          >
            Restart ADB
          </Button>
        )}
        
        <Button
          size="small"
          variant="outlined"
          startIcon={<Info />}
          onClick={() => setDetailsOpen(true)}
        >
          Details
        </Button>
      </Box>

      {/* Details Dialog */}
      <Dialog
        open={detailsOpen}
        onClose={() => setDetailsOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>ADB Status Details</DialogTitle>
        <DialogContent>
          <List>
            <ListItem>
              <ListItemText
                primary="Status"
                secondary={adbStatus.isAvailable ? 'Available' : 'Not Available'}
              />
            </ListItem>
            <Divider />
            
            {adbStatus.version && (
              <>
                <ListItem>
                  <ListItemText
                    primary="Version"
                    secondary={adbStatus.version}
                  />
                </ListItem>
                <Divider />
              </>
            )}
            
            {adbStatus.path && (
              <>
                <ListItem>
                  <ListItemText
                    primary="Binary Path"
                    secondary={adbStatus.path}
                  />
                </ListItem>
                <Divider />
              </>
            )}
            
            <ListItem>
              <ListItemText
                primary="Type"
                secondary="Bundled with Application"
              />
            </ListItem>
            <Divider />
            
            {adbStatus.error && (
              <>
                <ListItem>
                  <ListItemText
                    primary="Error"
                    secondary={adbStatus.error}
                  />
                </ListItem>
                <Divider />
              </>
            )}
            
            <ListItem>
              <ListItemText
                primary="Description"
                secondary="Android Debug Bridge (ADB) is bundled with this application. No separate installation required."
              />
            </ListItem>
          </List>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDetailsOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default ADBStatus;
