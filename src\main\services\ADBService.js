const { spawn, exec } = require('child_process');
const path = require('path');
const fs = require('fs-extra');
const os = require('os');
const { app } = require('electron');

class ADBService {
  constructor() {
    this.adbPath = this.getBundledAdbPath();
    this.devices = new Map();
    this.isInitialized = false;
  }

  getBundledAdbPath() {
    const platform = os.platform();
    const isDev = !app.isPackaged;

    // Get the base path for resources
    let resourcesPath;
    if (isDev) {
      // In development, resources are in the project root
      resourcesPath = path.join(__dirname, '..', '..', '..', 'resources');
    } else {
      // In production, resources are bundled with the app
      resourcesPath = path.join(process.resourcesPath, 'resources');
    }

    const adbResourcesPath = path.join(resourcesPath, 'adb');

    let adbBinaryPath;
    switch (platform) {
      case 'win32':
        adbBinaryPath = path.join(adbResourcesPath, 'win32', 'adb.exe');
        break;
      case 'darwin':
        adbBinaryPath = path.join(adbResourcesPath, 'darwin', 'adb');
        break;
      case 'linux':
        adbBinaryPath = path.join(adbResourcesPath, 'linux', 'adb');
        break;
      default:
        throw new Error(`Unsupported platform: ${platform}`);
    }

    // Verify the bundled ADB exists
    if (!fs.existsSync(adbBinaryPath)) {
      console.warn(`Bundled ADB not found at ${adbBinaryPath}, falling back to system ADB`);
      return this.findSystemAdbPath();
    }

    // Make sure the binary is executable on Unix systems
    if (platform !== 'win32') {
      try {
        fs.chmodSync(adbBinaryPath, 0o755);
      } catch (error) {
        console.warn('Failed to make ADB executable:', error.message);
      }
    }

    console.log(`Using bundled ADB at: ${adbBinaryPath}`);
    return adbBinaryPath;
  }

  findSystemAdbPath() {
    // Try to find ADB in common locations
    const possiblePaths = [
      // Windows
      'C:\\Program Files\\Android\\android-sdk\\platform-tools\\adb.exe',
      'C:\\Android\\android-sdk\\platform-tools\\adb.exe',
      'C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platform-tools\\adb.exe',
      // macOS
      '/Users/' + os.userInfo().username + '/Library/Android/sdk/platform-tools/adb',
      '/opt/homebrew/bin/adb',
      // Linux
      '/usr/bin/adb',
      '/usr/local/bin/adb',
      '/home/' + os.userInfo().username + '/Android/Sdk/platform-tools/adb'
    ];

    for (const adbPath of possiblePaths) {
      if (fs.existsSync(adbPath)) {
        return adbPath;
      }
    }

    // Try to find in PATH
    return 'adb';
  }

  async initialize() {
    try {
      console.log(`Initializing ADB Service with binary at: ${this.adbPath}`);
      await this.checkADBVersion();
      await this.startADBServer();
      this.isInitialized = true;
      console.log('ADB Service initialized successfully');
      return true;
    } catch (error) {
      console.error('Failed to initialize ADB Service:', error);

      // Provide more helpful error messages
      if (error.code === 'ENOENT') {
        throw new Error('ADB binary not found. The application may not have downloaded ADB binaries correctly. Please restart the application or reinstall.');
      } else if (error.code === 'EACCES') {
        throw new Error('Permission denied accessing ADB binary. Please check file permissions.');
      } else {
        throw new Error(`ADB initialization failed: ${error.message}`);
      }
    }
  }

  async checkADBVersion() {
    return new Promise((resolve, reject) => {
      exec(`"${this.adbPath}" version`, (error, stdout, stderr) => {
        if (error) {
          reject(new Error('ADB not found or not working'));
          return;
        }
        console.log('ADB Version:', stdout.trim());
        resolve(stdout.trim());
      });
    });
  }

  async startADBServer() {
    return new Promise((resolve, reject) => {
      exec(`"${this.adbPath}" start-server`, (error, stdout, stderr) => {
        if (error) {
          reject(error);
          return;
        }
        resolve();
      });
    });
  }

  async executeCommand(command, deviceId = null) {
    return new Promise((resolve, reject) => {
      const fullCommand = deviceId 
        ? `"${this.adbPath}" -s ${deviceId} ${command}`
        : `"${this.adbPath}" ${command}`;

      exec(fullCommand, { encoding: 'utf8', maxBuffer: 1024 * 1024 * 10 }, (error, stdout, stderr) => {
        if (error) {
          reject(new Error(`ADB command failed: ${error.message}`));
          return;
        }
        resolve(stdout.trim());
      });
    });
  }

  async listDevices() {
    try {
      const output = await this.executeCommand('devices -l');
      const lines = output.split('\n').slice(1); // Skip header
      const devices = [];

      for (const line of lines) {
        if (line.trim() && !line.includes('List of devices')) {
          const parts = line.trim().split(/\s+/);
          if (parts.length >= 2) {
            const deviceId = parts[0];
            const status = parts[1];
            
            if (status === 'device' || status === 'unauthorized') {
              const device = {
                id: deviceId,
                status: status,
                model: this.extractProperty(line, 'model:'),
                product: this.extractProperty(line, 'product:'),
                device: this.extractProperty(line, 'device:'),
                transport_id: this.extractProperty(line, 'transport_id:')
              };
              devices.push(device);
            }
          }
        }
      }

      return devices;
    } catch (error) {
      console.error('Failed to list devices:', error);
      return [];
    }
  }

  extractProperty(line, property) {
    const regex = new RegExp(`${property}([^\\s]+)`);
    const match = line.match(regex);
    return match ? match[1] : '';
  }

  async getDeviceInfo(deviceId) {
    try {
      const [manufacturer, model, version, sdk, serial, battery] = await Promise.all([
        this.executeCommand('shell getprop ro.product.manufacturer', deviceId).catch(() => 'Unknown'),
        this.executeCommand('shell getprop ro.product.model', deviceId).catch(() => 'Unknown'),
        this.executeCommand('shell getprop ro.build.version.release', deviceId).catch(() => 'Unknown'),
        this.executeCommand('shell getprop ro.build.version.sdk', deviceId).catch(() => 'Unknown'),
        this.executeCommand('shell getprop ro.serialno', deviceId).catch(() => 'Unknown'),
        this.executeCommand('shell dumpsys battery | grep level', deviceId).catch(() => 'Unknown')
      ]);

      const batteryLevel = battery.includes('level:') 
        ? battery.split('level:')[1].trim() + '%'
        : 'Unknown';

      return {
        id: deviceId,
        manufacturer: manufacturer.trim(),
        model: model.trim(),
        androidVersion: version.trim(),
        sdkVersion: sdk.trim(),
        serial: serial.trim(),
        batteryLevel: batteryLevel,
        connected: true
      };
    } catch (error) {
      console.error('Failed to get device info:', error);
      throw error;
    }
  }

  async listFiles(deviceId, remotePath = '/sdcard') {
    try {
      const output = await this.executeCommand(`shell ls -la "${remotePath}"`, deviceId);
      const lines = output.split('\n');
      const files = [];

      for (const line of lines) {
        if (line.trim() && !line.startsWith('total')) {
          const parts = line.trim().split(/\s+/);
          if (parts.length >= 8) {
            const permissions = parts[0];
            const size = parts[4];
            const date = `${parts[5]} ${parts[6]} ${parts[7]}`;
            const name = parts.slice(8).join(' ');

            if (name !== '.' && name !== '..') {
              files.push({
                name: name,
                path: path.posix.join(remotePath, name),
                size: permissions.startsWith('d') ? 0 : parseInt(size) || 0,
                isDirectory: permissions.startsWith('d'),
                permissions: permissions,
                modified: date,
                type: this.getFileType(name, permissions.startsWith('d'))
              });
            }
          }
        }
      }

      return files.sort((a, b) => {
        if (a.isDirectory && !b.isDirectory) return -1;
        if (!a.isDirectory && b.isDirectory) return 1;
        return a.name.localeCompare(b.name);
      });
    } catch (error) {
      console.error('Failed to list files:', error);
      throw error;
    }
  }

  getFileType(filename, isDirectory) {
    if (isDirectory) return 'folder';
    
    const ext = path.extname(filename).toLowerCase();
    const typeMap = {
      '.apk': 'application',
      '.jpg': 'image', '.jpeg': 'image', '.png': 'image', '.gif': 'image', '.bmp': 'image',
      '.mp4': 'video', '.avi': 'video', '.mkv': 'video', '.mov': 'video', '.wmv': 'video',
      '.mp3': 'audio', '.wav': 'audio', '.flac': 'audio', '.aac': 'audio', '.ogg': 'audio',
      '.txt': 'text', '.log': 'text', '.xml': 'text', '.json': 'text', '.csv': 'text',
      '.zip': 'archive', '.rar': 'archive', '.7z': 'archive', '.tar': 'archive',
      '.pdf': 'document', '.doc': 'document', '.docx': 'document', '.xls': 'document'
    };
    
    return typeMap[ext] || 'file';
  }

  async createFolder(deviceId, remotePath, folderName) {
    try {
      const fullPath = path.posix.join(remotePath, folderName);
      await this.executeCommand(`shell mkdir -p "${fullPath}"`, deviceId);
      return { success: true, path: fullPath };
    } catch (error) {
      console.error('Failed to create folder:', error);
      throw error;
    }
  }

  async deleteFile(deviceId, remotePath) {
    try {
      await this.executeCommand(`shell rm -rf "${remotePath}"`, deviceId);
      return { success: true };
    } catch (error) {
      console.error('Failed to delete file:', error);
      throw error;
    }
  }

  async renameFile(deviceId, oldPath, newPath) {
    try {
      await this.executeCommand(`shell mv "${oldPath}" "${newPath}"`, deviceId);
      return { success: true };
    } catch (error) {
      console.error('Failed to rename file:', error);
      throw error;
    }
  }

  async pullFile(deviceId, remotePath, localPath) {
    return new Promise((resolve, reject) => {
      const command = `"${this.adbPath}" -s ${deviceId} pull "${remotePath}" "${localPath}"`;
      const process = spawn(command, { shell: true });
      
      let output = '';
      let error = '';

      process.stdout.on('data', (data) => {
        output += data.toString();
      });

      process.stderr.on('data', (data) => {
        error += data.toString();
      });

      process.on('close', (code) => {
        if (code === 0) {
          resolve({ success: true, output });
        } else {
          reject(new Error(`Pull failed: ${error}`));
        }
      });
    });
  }

  async pushFile(deviceId, localPath, remotePath) {
    return new Promise((resolve, reject) => {
      const command = `"${this.adbPath}" -s ${deviceId} push "${localPath}" "${remotePath}"`;
      const process = spawn(command, { shell: true });
      
      let output = '';
      let error = '';

      process.stdout.on('data', (data) => {
        output += data.toString();
      });

      process.stderr.on('data', (data) => {
        error += data.toString();
      });

      process.on('close', (code) => {
        if (code === 0) {
          resolve({ success: true, output });
        } else {
          reject(new Error(`Push failed: ${error}`));
        }
      });
    });
  }

  startLogcatStream(deviceId, command) {
    const fullCommand = `"${this.adbPath}" -s ${deviceId} ${command}`;
    const process = spawn(fullCommand, { shell: true });

    process.stdout.on('data', (data) => {
      const output = data.toString();
      const lines = output.split('\n').filter(line => line.trim());

      const logs = lines.map(line => {
        // Parse logcat format: MM-DD HH:MM:SS.mmm PID TID PRIORITY TAG: MESSAGE
        const match = line.match(/^(\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})\s+(\d+)\s+(\d+)\s+([VDIWEF])\s+([^:]+):\s*(.*)$/);

        if (match) {
          return {
            time: match[1],
            pid: match[2],
            tid: match[3],
            level: match[4],
            tag: match[5],
            message: match[6]
          };
        }

        return {
          time: new Date().toISOString().slice(11, 23),
          level: 'I',
          tag: 'System',
          message: line
        };
      });

      // Emit logcat data event
      if (global.mainWindow) {
        global.mainWindow.webContents.send('logcat-data', { logs });
      }
    });

    process.stderr.on('data', (data) => {
      console.error('Logcat error:', data.toString());
    });

    return process;
  }
}

module.exports = ADBService;
