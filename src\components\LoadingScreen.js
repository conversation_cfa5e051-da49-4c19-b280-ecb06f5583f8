import React from 'react';
import { Box, CircularProgress, Typography, LinearProgress } from '@mui/material';
import { Android } from '@mui/icons-material';

function LoadingScreen() {
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        backgroundColor: 'background.default',
      }}
    >
      <Box sx={{ textAlign: 'center', mb: 4 }}>
        <Android sx={{ fontSize: 80, color: 'primary.main', mb: 2 }} />
        <Typography variant="h4" component="h1" gutterBottom>
          Android Manager
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Initializing application...
        </Typography>
      </Box>
      
      <Box sx={{ width: 300, mb: 2 }}>
        <LinearProgress />
      </Box>
      
      <Typography variant="body2" color="text.secondary">
        Please wait while we set up your device management environment
      </Typography>
    </Box>
  );
}

export default LoadingScreen;
