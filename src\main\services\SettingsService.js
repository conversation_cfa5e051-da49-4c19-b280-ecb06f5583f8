const fs = require('fs-extra');
const path = require('path');
const os = require('os');
const { app } = require('electron');

class SettingsService {
  constructor(storageService) {
    this.storage = storageService;
    this.defaultSettings = {
      theme: 'light',
      language: 'en',
      notifications: true,
      autoUpdate: true,
      transferTimeout: 30,
      maxConcurrentTransfers: 3,
      defaultDownloadPath: path.join(os.homedir(), 'Downloads'),
      autoBackup: false,
      logLevel: 'info',
      keepLogDays: 7,
      compressionLevel: 5,
      verifyTransfers: true,
      showHiddenFiles: false,
      autoRefreshInterval: 5
    };
  }

  async getSettings() {
    try {
      const settings = await this.storage.getAllSettings();
      return { ...this.defaultSettings, ...settings };
    } catch (error) {
      console.error('Failed to load settings:', error);
      return this.defaultSettings;
    }
  }

  async saveSettings(settings) {
    try {
      const mergedSettings = { ...this.defaultSettings, ...settings };
      for (const [key, value] of Object.entries(mergedSettings)) {
        await this.storage.updateSetting(key, value);
      }
      return { success: true };
    } catch (error) {
      console.error('Failed to save settings:', error);
      throw error;
    }
  }

  async resetSettings() {
    try {
      await this.storage.clearFile('settings.json');
      for (const [key, value] of Object.entries(this.defaultSettings)) {
        await this.storage.updateSetting(key, value);
      }
      return { success: true };
    } catch (error) {
      console.error('Failed to reset settings:', error);
      throw error;
    }
  }

  async exportSettings() {
    try {
      const settings = await this.getSettings();
      return {
        success: true,
        data: JSON.stringify(settings, null, 2)
      };
    } catch (error) {
      console.error('Failed to export settings:', error);
      throw error;
    }
  }

  async importSettings(settingsData) {
    try {
      const settings = JSON.parse(settingsData);
      await this.saveSettings(settings);
      return { success: true };
    } catch (error) {
      console.error('Failed to import settings:', error);
      throw error;
    }
  }

  async getSetting(key) {
    try {
      const settings = await this.getSettings();
      return settings[key];
    } catch (error) {
      console.error(`Failed to get setting ${key}:`, error);
      return this.defaultSettings[key];
    }
  }

  async setSetting(key, value) {
    try {
      const settings = await this.getSettings();
      settings[key] = value;
      await this.saveSettings(settings);
      return { success: true };
    } catch (error) {
      console.error(`Failed to set setting ${key}:`, error);
      throw error;
    }
  }

  async validateSettings(settings) {
    const errors = [];

    // Validate theme
    if (settings.theme && !['light', 'dark', 'auto'].includes(settings.theme)) {
      errors.push('Invalid theme value');
    }

    // Validate language
    if (settings.language && !['en', 'es', 'fr', 'de', 'zh', 'ja'].includes(settings.language)) {
      errors.push('Invalid language value');
    }

    // Validate numeric values
    if (settings.transferTimeout && (settings.transferTimeout < 10 || settings.transferTimeout > 300)) {
      errors.push('Transfer timeout must be between 10 and 300 seconds');
    }

    if (settings.maxConcurrentTransfers && (settings.maxConcurrentTransfers < 1 || settings.maxConcurrentTransfers > 10)) {
      errors.push('Max concurrent transfers must be between 1 and 10');
    }

    if (settings.keepLogDays && (settings.keepLogDays < 1 || settings.keepLogDays > 30)) {
      errors.push('Keep log days must be between 1 and 30');
    }

    if (settings.autoRefreshInterval && (settings.autoRefreshInterval < 1 || settings.autoRefreshInterval > 60)) {
      errors.push('Auto refresh interval must be between 1 and 60 seconds');
    }

    // Validate log level
    if (settings.logLevel && !['error', 'warn', 'info', 'debug'].includes(settings.logLevel)) {
      errors.push('Invalid log level');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  async backupSettings() {
    try {
      const settings = await this.getSettings();
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupPath = path.join(
        path.dirname(this.settingsPath),
        `settings-backup-${timestamp}.json`
      );
      
      await fs.writeJson(backupPath, settings, { spaces: 2 });
      return { success: true, backupPath };
    } catch (error) {
      console.error('Failed to backup settings:', error);
      throw error;
    }
  }

  async restoreSettings(backupPath) {
    try {
      if (!await fs.pathExists(backupPath)) {
        throw new Error('Backup file not found');
      }

      const settings = await fs.readJson(backupPath);
      const validation = await this.validateSettings(settings);
      
      if (!validation.isValid) {
        throw new Error(`Invalid settings: ${validation.errors.join(', ')}`);
      }

      await this.saveSettings(settings);
      return { success: true };
    } catch (error) {
      console.error('Failed to restore settings:', error);
      throw error;
    }
  }

  async cleanupOldBackups() {
    try {
      const settingsDir = path.dirname(this.settingsPath);
      const files = await fs.readdir(settingsDir);
      const backupFiles = files.filter(file => file.startsWith('settings-backup-'));
      
      // Keep only the last 10 backups
      if (backupFiles.length > 10) {
        const sortedBackups = backupFiles.sort().slice(0, -10);
        for (const backup of sortedBackups) {
          await fs.remove(path.join(settingsDir, backup));
        }
      }
      
      return { success: true };
    } catch (error) {
      console.error('Failed to cleanup old backups:', error);
      throw error;
    }
  }

  async getSettingsInfo() {
    try {
      const settings = await this.getSettings();
      const stats = await fs.stat(this.settingsPath).catch(() => null);
      
      return {
        success: true,
        settingsPath: this.settingsPath,
        lastModified: stats ? stats.mtime.toISOString() : null,
        size: stats ? stats.size : 0,
        settingsCount: Object.keys(settings).length
      };
    } catch (error) {
      console.error('Failed to get settings info:', error);
      throw error;
    }
  }
}

module.exports = SettingsService;
