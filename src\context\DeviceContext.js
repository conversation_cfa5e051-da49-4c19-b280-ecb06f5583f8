import React, { createContext, useContext, useReducer, useEffect } from 'react';

// Initial state
const initialState = {
  devices: [],
  selectedDevice: null,
  connectedDevices: [],
  deviceInfo: {},
  loading: false,
  error: null,
  refreshing: false
};

// Action types
const ActionTypes = {
  SET_LOADING: 'SET_LOADING',
  SET_REFRESHING: 'SET_REFRESHING',
  SET_DEVICES: 'SET_DEVICES',
  SET_SELECTED_DEVICE: 'SET_SELECTED_DEVICE',
  SET_DEVICE_INFO: 'SET_DEVICE_INFO',
  ADD_DEVICE: 'ADD_DEVICE',
  REMOVE_DEVICE: 'REMOVE_DEVICE',
  UPDATE_DEVICE: 'UPDATE_DEVICE',
  SET_ERROR: 'SET_ERROR',
  CLEAR_ERROR: 'CLEAR_ERROR'
};

// Reducer
function deviceReducer(state, action) {
  switch (action.type) {
    case ActionTypes.SET_LOADING:
      return { ...state, loading: action.payload };
    
    case ActionTypes.SET_REFRESHING:
      return { ...state, refreshing: action.payload };
    
    case ActionTypes.SET_DEVICES:
      return { 
        ...state, 
        devices: action.payload,
        connectedDevices: action.payload.filter(device => device.status === 'device'),
        loading: false,
        refreshing: false
      };
    
    case ActionTypes.SET_SELECTED_DEVICE:
      return { ...state, selectedDevice: action.payload };
    
    case ActionTypes.SET_DEVICE_INFO:
      return { 
        ...state, 
        deviceInfo: { 
          ...state.deviceInfo, 
          [action.payload.deviceId]: action.payload.info 
        }
      };
    
    case ActionTypes.ADD_DEVICE:
      const newDevices = [...state.devices];
      const existingIndex = newDevices.findIndex(d => d.id === action.payload.id);
      
      if (existingIndex >= 0) {
        newDevices[existingIndex] = action.payload;
      } else {
        newDevices.push(action.payload);
      }
      
      return {
        ...state,
        devices: newDevices,
        connectedDevices: newDevices.filter(device => device.status === 'device')
      };
    
    case ActionTypes.REMOVE_DEVICE:
      const filteredDevices = state.devices.filter(device => device.id !== action.payload);
      return {
        ...state,
        devices: filteredDevices,
        connectedDevices: filteredDevices.filter(device => device.status === 'device'),
        selectedDevice: state.selectedDevice?.id === action.payload ? null : state.selectedDevice
      };
    
    case ActionTypes.UPDATE_DEVICE:
      const updatedDevices = state.devices.map(device =>
        device.id === action.payload.id ? { ...device, ...action.payload } : device
      );
      
      return {
        ...state,
        devices: updatedDevices,
        connectedDevices: updatedDevices.filter(device => device.status === 'device')
      };
    
    case ActionTypes.SET_ERROR:
      return { ...state, error: action.payload, loading: false, refreshing: false };
    
    case ActionTypes.CLEAR_ERROR:
      return { ...state, error: null };
    
    default:
      return state;
  }
}

// Context
const DeviceContext = createContext();

// Provider component
export function DeviceProvider({ children }) {
  const [state, dispatch] = useReducer(deviceReducer, initialState);

  // Actions
  const actions = {
    setLoading: (loading) => {
      dispatch({ type: ActionTypes.SET_LOADING, payload: loading });
    },

    setRefreshing: (refreshing) => {
      dispatch({ type: ActionTypes.SET_REFRESHING, payload: refreshing });
    },

    setError: (error) => {
      dispatch({ type: ActionTypes.SET_ERROR, payload: error });
    },

    clearError: () => {
      dispatch({ type: ActionTypes.CLEAR_ERROR });
    },

    refreshDevices: async () => {
      try {
        if (!window.electronAPI?.device?.list) {
          console.warn('Device API not available - running in development mode');
          return;
        }
        dispatch({ type: ActionTypes.SET_REFRESHING, payload: true });
        const devices = await window.electronAPI.device.list();
        dispatch({ type: ActionTypes.SET_DEVICES, payload: devices });
      } catch (error) {
        console.error('Failed to refresh devices:', error);
        dispatch({ type: ActionTypes.SET_ERROR, payload: error.message });
      }
    },

    selectDevice: async (device) => {
      try {
        if (device) {
          if (!window.electronAPI?.device?.getInfo) {
            console.warn('Device API not available - running in development mode');
            return;
          }
          dispatch({ type: ActionTypes.SET_LOADING, payload: true });

          // Connect to device and get detailed info
          const deviceInfo = await window.electronAPI.device.getInfo(device.id);

          dispatch({ type: ActionTypes.SET_SELECTED_DEVICE, payload: device });
          dispatch({ type: ActionTypes.SET_DEVICE_INFO, payload: { deviceId: device.id, info: deviceInfo } });
          dispatch({ type: ActionTypes.SET_LOADING, payload: false });
        } else {
          dispatch({ type: ActionTypes.SET_SELECTED_DEVICE, payload: null });
        }
      } catch (error) {
        console.error('Failed to select device:', error);
        dispatch({ type: ActionTypes.SET_ERROR, payload: error.message });
      }
    },

    connectDevice: async (deviceId) => {
      try {
        if (!window.electronAPI?.device?.connect) {
          console.warn('Device API not available - running in development mode');
          return { success: false, message: 'Device API not available' };
        }
        const result = await window.electronAPI.device.connect(deviceId);
        if (result.success) {
          dispatch({ type: ActionTypes.UPDATE_DEVICE, payload: { id: deviceId, status: 'device' } });
        }
        return result;
      } catch (error) {
        console.error('Failed to connect device:', error);
        dispatch({ type: ActionTypes.SET_ERROR, payload: error.message });
        throw error;
      }
    },

    disconnectDevice: async (deviceId) => {
      try {
        if (!window.electronAPI?.device?.disconnect) {
          console.warn('Device API not available - running in development mode');
          return { success: false, message: 'Device API not available' };
        }
        const result = await window.electronAPI.device.disconnect(deviceId);
        if (result.success) {
          dispatch({ type: ActionTypes.UPDATE_DEVICE, payload: { id: deviceId, status: 'offline' } });
        }
        return result;
      } catch (error) {
        console.error('Failed to disconnect device:', error);
        dispatch({ type: ActionTypes.SET_ERROR, payload: error.message });
        throw error;
      }
    },

    getDeviceInfo: async (deviceId) => {
      try {
        if (!window.electronAPI?.device?.getInfo) {
          console.warn('Device API not available - running in development mode');
          return null;
        }
        const info = await window.electronAPI.device.getInfo(deviceId);
        dispatch({ type: ActionTypes.SET_DEVICE_INFO, payload: { deviceId, info } });
        return info;
      } catch (error) {
        console.error('Failed to get device info:', error);
        dispatch({ type: ActionTypes.SET_ERROR, payload: error.message });
        throw error;
      }
    },

    addDevice: (device) => {
      dispatch({ type: ActionTypes.ADD_DEVICE, payload: device });
    },

    removeDevice: (deviceId) => {
      dispatch({ type: ActionTypes.REMOVE_DEVICE, payload: deviceId });
    },

    updateDevice: (device) => {
      dispatch({ type: ActionTypes.UPDATE_DEVICE, payload: device });
    }
  };

  // Auto-refresh devices on mount and setup event listeners
  useEffect(() => {
    // Check if electronAPI is available
    if (!window.electronAPI) {
      console.warn('Device API not available - running in development mode');
      return;
    }

    actions.refreshDevices();

    // Setup device event listeners
    const removeDeviceConnectedListener = window.electronAPI.onDeviceConnected((device) => {
      actions.addDevice(device);
    });

    const removeDeviceDisconnectedListener = window.electronAPI.onDeviceDisconnected((deviceId) => {
      actions.removeDevice(deviceId);
    });

    // Auto-refresh every 10 seconds
    const refreshInterval = setInterval(() => {
      if (!state.loading && !state.refreshing) {
        actions.refreshDevices();
      }
    }, 10000);

    return () => {
      removeDeviceConnectedListener();
      removeDeviceDisconnectedListener();
      clearInterval(refreshInterval);
    };
  }, []);

  const value = {
    ...state,
    ...actions
  };

  return (
    <DeviceContext.Provider value={value}>
      {children}
    </DeviceContext.Provider>
  );
}

// Hook to use device context
export function useDevice() {
  const context = useContext(DeviceContext);
  if (!context) {
    throw new Error('useDevice must be used within a DeviceProvider');
  }
  return context;
}

export default DeviceContext;
