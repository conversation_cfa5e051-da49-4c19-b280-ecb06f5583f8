const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs-extra');
const { v4: uuidv4 } = require('uuid');
const EventEmitter = require('events');

class TransferService extends EventEmitter {
  constructor(storageService) {
    super();
    this.storage = storageService;
    this.activeTransfers = new Map();
    this.transferQueue = [];
    this.maxConcurrentTransfers = 3;
    this.currentTransfers = 0;
  }

  async uploadFiles(deviceId, localPaths, remotePath) {
    const transferId = uuidv4();
    const transfer = {
      id: transferId,
      type: 'upload',
      deviceId,
      files: localPaths.map(localPath => ({
        localPath,
        remotePath: path.posix.join(remotePath, path.basename(localPath)),
        status: 'pending',
        progress: 0,
        size: 0,
        transferred: 0
      })),
      status: 'queued',
      createdAt: new Date().toISOString(),
      totalSize: 0,
      totalTransferred: 0,
      progress: 0
    };

    // Calculate total size
    for (const file of transfer.files) {
      try {
        const stats = await fs.stat(file.localPath);
        file.size = stats.size;
        transfer.totalSize += stats.size;
      } catch (error) {
        console.error(`Failed to get size for ${file.localPath}:`, error);
        file.status = 'error';
        file.error = error.message;
      }
    }

    this.transferQueue.push(transfer);
    this.activeTransfers.set(transferId, transfer);
    
    // Save to storage
    await this.storage.appendToArray('transfers.json', transfer);
    
    // Start processing queue
    this.processQueue();
    
    return { transferId, transfer };
  }

  async downloadFiles(deviceId, remotePaths, localPath) {
    const transferId = uuidv4();
    const transfer = {
      id: transferId,
      type: 'download',
      deviceId,
      files: remotePaths.map(remotePath => ({
        remotePath,
        localPath: path.join(localPath, path.basename(remotePath)),
        status: 'pending',
        progress: 0,
        size: 0,
        transferred: 0
      })),
      status: 'queued',
      createdAt: new Date().toISOString(),
      totalSize: 0,
      totalTransferred: 0,
      progress: 0
    };

    // For downloads, we'll get file sizes during transfer
    this.transferQueue.push(transfer);
    this.activeTransfers.set(transferId, transfer);
    
    // Save to storage
    await this.storage.appendToArray('transfers.json', transfer);
    
    // Start processing queue
    this.processQueue();
    
    return { transferId, transfer };
  }

  async processQueue() {
    if (this.currentTransfers >= this.maxConcurrentTransfers || this.transferQueue.length === 0) {
      return;
    }

    const transfer = this.transferQueue.shift();
    if (!transfer) return;

    this.currentTransfers++;
    transfer.status = 'active';
    transfer.startedAt = new Date().toISOString();

    this.emit('transferStarted', transfer);
    
    try {
      if (transfer.type === 'upload') {
        await this.executeUpload(transfer);
      } else {
        await this.executeDownload(transfer);
      }
      
      transfer.status = 'completed';
      transfer.completedAt = new Date().toISOString();
      this.emit('transferCompleted', transfer);
      
    } catch (error) {
      transfer.status = 'failed';
      transfer.error = error.message;
      transfer.completedAt = new Date().toISOString();
      this.emit('transferFailed', transfer);
    }

    this.currentTransfers--;
    await this.storage.updateArrayItem('transfers.json', item => item.id === transfer.id, transfer);
    
    // Process next in queue
    setImmediate(() => this.processQueue());
  }

  async executeUpload(transfer) {
    const adbPath = this.getADBPath();
    
    for (const file of transfer.files) {
      if (transfer.status === 'cancelled') break;
      if (file.status === 'error') continue;

      file.status = 'transferring';
      this.updateTransferProgress(transfer);

      try {
        await this.uploadSingleFile(adbPath, transfer.deviceId, file);
        file.status = 'completed';
        file.progress = 100;
        file.transferred = file.size;
      } catch (error) {
        file.status = 'error';
        file.error = error.message;
        console.error(`Upload failed for ${file.localPath}:`, error);
      }

      this.updateTransferProgress(transfer);
    }
  }

  async executeDownload(transfer) {
    const adbPath = this.getADBPath();
    
    for (const file of transfer.files) {
      if (transfer.status === 'cancelled') break;
      if (file.status === 'error') continue;

      file.status = 'transferring';
      this.updateTransferProgress(transfer);

      try {
        await this.downloadSingleFile(adbPath, transfer.deviceId, file);
        file.status = 'completed';
        file.progress = 100;
        
        // Get actual file size after download
        const stats = await fs.stat(file.localPath);
        file.size = stats.size;
        file.transferred = file.size;
        transfer.totalSize += file.size;
        
      } catch (error) {
        file.status = 'error';
        file.error = error.message;
        console.error(`Download failed for ${file.remotePath}:`, error);
      }

      this.updateTransferProgress(transfer);
    }
  }

  async uploadSingleFile(adbPath, deviceId, file) {
    return new Promise((resolve, reject) => {
      const command = `"${adbPath}" -s ${deviceId} push "${file.localPath}" "${file.remotePath}"`;
      const process = spawn(command, { shell: true });
      
      let output = '';
      let lastProgress = 0;

      process.stdout.on('data', (data) => {
        output += data.toString();
        
        // Parse progress from ADB output
        const progressMatch = output.match(/(\d+)%/);
        if (progressMatch) {
          const progress = parseInt(progressMatch[1]);
          if (progress > lastProgress) {
            lastProgress = progress;
            file.progress = progress;
            file.transferred = Math.floor((progress / 100) * file.size);
            this.emit('fileProgress', { transferId: file.transferId, file });
          }
        }
      });

      process.stderr.on('data', (data) => {
        console.error('ADB push error:', data.toString());
      });

      process.on('close', (code) => {
        if (code === 0) {
          resolve();
        } else {
          reject(new Error(`Upload failed with code ${code}`));
        }
      });

      process.on('error', (error) => {
        reject(error);
      });
    });
  }

  async downloadSingleFile(adbPath, deviceId, file) {
    return new Promise((resolve, reject) => {
      const command = `"${adbPath}" -s ${deviceId} pull "${file.remotePath}" "${file.localPath}"`;
      const process = spawn(command, { shell: true });
      
      let output = '';
      let lastProgress = 0;

      process.stdout.on('data', (data) => {
        output += data.toString();
        
        // Parse progress from ADB output
        const progressMatch = output.match(/(\d+)%/);
        if (progressMatch) {
          const progress = parseInt(progressMatch[1]);
          if (progress > lastProgress) {
            lastProgress = progress;
            file.progress = progress;
            this.emit('fileProgress', { transferId: file.transferId, file });
          }
        }
      });

      process.stderr.on('data', (data) => {
        console.error('ADB pull error:', data.toString());
      });

      process.on('close', (code) => {
        if (code === 0) {
          resolve();
        } else {
          reject(new Error(`Download failed with code ${code}`));
        }
      });

      process.on('error', (error) => {
        reject(error);
      });
    });
  }

  updateTransferProgress(transfer) {
    const completedFiles = transfer.files.filter(f => f.status === 'completed');
    const totalFiles = transfer.files.length;
    
    transfer.totalTransferred = transfer.files.reduce((sum, file) => sum + file.transferred, 0);
    transfer.progress = transfer.totalSize > 0 
      ? Math.floor((transfer.totalTransferred / transfer.totalSize) * 100)
      : Math.floor((completedFiles.length / totalFiles) * 100);

    this.emit('transferProgress', transfer);
  }

  async cancelTransfer(transferId) {
    const transfer = this.activeTransfers.get(transferId);
    if (!transfer) {
      throw new Error('Transfer not found');
    }

    transfer.status = 'cancelled';
    transfer.cancelledAt = new Date().toISOString();
    
    // Remove from queue if not started
    const queueIndex = this.transferQueue.findIndex(t => t.id === transferId);
    if (queueIndex !== -1) {
      this.transferQueue.splice(queueIndex, 1);
    }

    await this.storage.updateArrayItem('transfers.json', item => item.id === transfer.id, transfer);
    this.emit('transferCancelled', transfer);
    
    return { success: true };
  }

  async getQueue() {
    return Array.from(this.activeTransfers.values())
      .filter(t => ['queued', 'active'].includes(t.status));
  }

  async getHistory(limit = 50) {
    const transfers = await this.storage.getAllArrayItems('transfers.json');
    return transfers.slice(0, limit);
  }

  getADBPath() {
    // This should match the ADB path finding logic from ADBService
    const os = require('os');
    const possiblePaths = [
      'C:\\Program Files\\Android\\android-sdk\\platform-tools\\adb.exe',
      'C:\\Android\\android-sdk\\platform-tools\\adb.exe',
      'C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platform-tools\\adb.exe',
      '/Users/' + os.userInfo().username + '/Library/Android/sdk/platform-tools/adb',
      '/opt/homebrew/bin/adb',
      '/usr/bin/adb',
      '/usr/local/bin/adb',
      '/home/' + os.userInfo().username + '/Android/Sdk/platform-tools/adb'
    ];

    for (const adbPath of possiblePaths) {
      if (fs.existsSync(adbPath)) {
        return adbPath;
      }
    }

    return 'adb'; // Fallback to PATH
  }

  async pauseTransfer(transferId) {
    const transfer = this.activeTransfers.get(transferId);
    if (!transfer) {
      throw new Error('Transfer not found');
    }

    if (transfer.status === 'active') {
      transfer.status = 'paused';
      transfer.pausedAt = new Date().toISOString();
      await this.storage.updateArrayItem('transfers.json', item => item.id === transfer.id, transfer);
      this.emit('transferPaused', transfer);
    }

    return { success: true };
  }

  async resumeTransfer(transferId) {
    const transfer = this.activeTransfers.get(transferId);
    if (!transfer) {
      throw new Error('Transfer not found');
    }

    if (transfer.status === 'paused') {
      transfer.status = 'queued';
      delete transfer.pausedAt;
      this.transferQueue.unshift(transfer); // Add to front of queue
      await this.storage.updateArrayItem('transfers.json', item => item.id === transfer.id, transfer);
      this.emit('transferResumed', transfer);
      this.processQueue();
    }

    return { success: true };
  }

  async retryTransfer(transferId) {
    try {
      // Get transfer from history
      const transfers = await this.storage.findArrayItems('transfers.json', item => item.id === transferId);
      const transfer = transfers.length > 0 ? transfers[0] : null;
      if (!transfer) {
        throw new Error('Transfer not found');
      }

      // Reset transfer status
      transfer.status = 'queued';
      transfer.progress = 0;
      transfer.totalTransferred = 0;
      transfer.error = null;
      transfer.retryCount = (transfer.retryCount || 0) + 1;
      transfer.retriedAt = new Date().toISOString();

      // Reset file statuses
      transfer.files.forEach(file => {
        file.status = 'pending';
        file.progress = 0;
        file.transferred = 0;
        file.error = null;
      });

      // Add back to queue
      this.activeTransfers.set(transferId, transfer);
      this.transferQueue.push(transfer);
      await this.storage.updateArrayItem('transfers.json', item => item.id === transfer.id, transfer);

      this.emit('transferRetried', transfer);
      this.processQueue();

      return { success: true };
    } catch (error) {
      console.error('Failed to retry transfer:', error);
      throw error;
    }
  }

  async clearHistory() {
    try {
      await this.storage.clearFile('transfers.json');
      return { success: true };
    } catch (error) {
      console.error('Failed to clear transfer history:', error);
      throw error;
    }
  }

  async getTransferStats() {
    try {
      const history = await this.storage.getAllArrayItems('transfers.json');
      const activeTransfers = Array.from(this.activeTransfers.values());

      const stats = {
        total: history.length + activeTransfers.length,
        active: activeTransfers.filter(t => t.status === 'active').length,
        queued: activeTransfers.filter(t => t.status === 'queued').length,
        completed: history.filter(t => t.status === 'completed').length,
        failed: history.filter(t => t.status === 'failed').length,
        cancelled: history.filter(t => t.status === 'cancelled').length,
        totalBytesTransferred: history.reduce((sum, t) => sum + (t.totalTransferred || 0), 0),
        averageSpeed: 0,
        successRate: 0
      };

      // Calculate success rate
      const finishedTransfers = stats.completed + stats.failed + stats.cancelled;
      if (finishedTransfers > 0) {
        stats.successRate = (stats.completed / finishedTransfers) * 100;
      }

      // Calculate average speed (simplified)
      const completedTransfers = history.filter(t => t.status === 'completed' && t.duration);
      if (completedTransfers.length > 0) {
        const totalSpeed = completedTransfers.reduce((sum, t) => {
          const speed = t.totalSize / (t.duration / 1000); // bytes per second
          return sum + speed;
        }, 0);
        stats.averageSpeed = totalSpeed / completedTransfers.length;
      }

      return stats;
    } catch (error) {
      console.error('Failed to get transfer stats:', error);
      throw error;
    }
  }
}

module.exports = TransferService;
