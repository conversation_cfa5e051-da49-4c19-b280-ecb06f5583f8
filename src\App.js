import React, { useState, useEffect } from 'react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { Box, Snackbar, Alert } from '@mui/material';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';

// Components
import Sidebar from './components/Sidebar';
import DeviceManager from './components/DeviceManager';
import FileBrowser from './components/FileBrowser';
import TransferManager from './components/TransferManager';
import ApplicationManager from './components/ApplicationManager';
import DeviceTools from './components/DeviceTools';
import Settings from './components/Settings';
import LoadingScreen from './components/LoadingScreen';
import ErrorBoundary from './components/ErrorBoundary';
import NotificationSystem from './components/NotificationSystem';

// Context
import { DeviceProvider } from './context/DeviceContext';
import { TransferProvider } from './context/TransferContext';
import { SettingsProvider } from './context/SettingsContext';

// Styles
import './App.css';

function App() {
  const [currentView, setCurrentView] = useState('devices');
  const [selectedDevice, setSelectedDevice] = useState(null);
  const [theme, setTheme] = useState('light');
  const [loading, setLoading] = useState(true);
  const [notification, setNotification] = useState({ open: false, message: '', severity: 'info' });

  // Create theme
  const muiTheme = createTheme({
    palette: {
      mode: theme,
      primary: {
        main: '#1976d2',
        light: '#42a5f5',
        dark: '#1565c0',
      },
      secondary: {
        main: '#dc004e',
        light: '#ff5983',
        dark: '#9a0036',
      },
      background: {
        default: theme === 'light' ? '#f5f5f5' : '#121212',
        paper: theme === 'light' ? '#ffffff' : '#1e1e1e',
      },
    },
    typography: {
      fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
      h4: {
        fontWeight: 600,
      },
      h6: {
        fontWeight: 500,
      },
    },
    components: {
      MuiDrawer: {
        styleOverrides: {
          paper: {
            backgroundColor: theme === 'light' ? '#fafafa' : '#1e1e1e',
            borderRight: `1px solid ${theme === 'light' ? '#e0e0e0' : '#333'}`,
          },
        },
      },
      MuiAppBar: {
        styleOverrides: {
          root: {
            backgroundColor: theme === 'light' ? '#1976d2' : '#1e1e1e',
          },
        },
      },
    },
  });

  useEffect(() => {
    // Initialize app
    const initializeApp = async () => {
      try {
        // Check if running in Electron
        if (!window.electronAPI) {
          console.warn('Not running in Electron environment. Some features may not work.');
          setLoading(false);
          return;
        }

        // Load settings
        const settings = await window.electronAPI.settings.get();
        setTheme(settings.theme || 'light');

        // Setup event listeners
        setupEventListeners();

        setLoading(false);
      } catch (error) {
        console.error('Failed to initialize app:', error);
        showNotification('Failed to initialize application', 'error');
        setLoading(false);
      }
    };

    initializeApp();
  }, []);

  const setupEventListeners = () => {
    // Check if electronAPI is available
    if (!window.electronAPI) {
      return () => {}; // Return empty cleanup function
    }

    // Device connection events
    const removeDeviceConnectedListener = window.electronAPI.onDeviceConnected((device) => {
      showNotification(`Device connected: ${device.model}`, 'success');
    });

    const removeDeviceDisconnectedListener = window.electronAPI.onDeviceDisconnected((deviceId) => {
      showNotification('Device disconnected', 'warning');
      if (selectedDevice && selectedDevice.id === deviceId) {
        setSelectedDevice(null);
        setCurrentView('devices');
      }
    });

    // Transfer events
    const removeTransferProgressListener = window.electronAPI.transfer.onProgress((data) => {
      // Transfer progress will be handled by TransferContext
    });

    const removeTransferCompleteListener = window.electronAPI.transfer.onComplete((data) => {
      showNotification(`Transfer completed: ${data.files.length} files`, 'success');
    });

    const removeTransferErrorListener = window.electronAPI.transfer.onError((data) => {
      showNotification(`Transfer failed: ${data.error}`, 'error');
    });

    // Cleanup function
    return () => {
      removeDeviceConnectedListener();
      removeDeviceDisconnectedListener();
      removeTransferProgressListener();
      removeTransferCompleteListener();
      removeTransferErrorListener();
    };
  };

  const showNotification = (message, severity = 'info') => {
    setNotification({ open: true, message, severity });
  };

  const handleCloseNotification = () => {
    setNotification({ ...notification, open: false });
  };

  const handleViewChange = (view) => {
    setCurrentView(view);
  };

  const handleDeviceSelect = (device) => {
    setSelectedDevice(device);
    if (device && currentView === 'devices') {
      setCurrentView('files');
    }
  };

  const handleThemeChange = (newTheme) => {
    setTheme(newTheme);
  };

  const renderCurrentView = () => {
    switch (currentView) {
      case 'devices':
        return (
          <DeviceManager 
            onDeviceSelect={handleDeviceSelect}
            selectedDevice={selectedDevice}
          />
        );
      case 'files':
        return (
          <FileBrowser 
            selectedDevice={selectedDevice}
            onShowNotification={showNotification}
          />
        );
      case 'transfers':
        return (
          <TransferManager 
            onShowNotification={showNotification}
          />
        );
      case 'applications':
        return (
          <ApplicationManager 
            selectedDevice={selectedDevice}
            onShowNotification={showNotification}
          />
        );
      case 'tools':
        return (
          <DeviceTools 
            selectedDevice={selectedDevice}
            onShowNotification={showNotification}
          />
        );
      case 'settings':
        return (
          <Settings 
            onThemeChange={handleThemeChange}
            onShowNotification={showNotification}
          />
        );
      default:
        return <DeviceManager onDeviceSelect={handleDeviceSelect} />;
    }
  };

  if (loading) {
    return <LoadingScreen />;
  }

  return (
    <ErrorBoundary
      onShowNotification={showNotification}
      onNavigateHome={() => setCurrentView('devices')}
    >
      <SettingsProvider>
        <DeviceProvider>
          <TransferProvider>
            <ThemeProvider theme={muiTheme}>
              <CssBaseline />
              <Box sx={{ display: 'flex', height: '100vh' }}>
                <Sidebar
                  currentView={currentView}
                  onViewChange={handleViewChange}
                  selectedDevice={selectedDevice}
                  theme={theme}
                />

                <Box
                  component="main"
                  sx={{
                    flexGrow: 1,
                    display: 'flex',
                    flexDirection: 'column',
                    overflow: 'hidden',
                  }}
                >
                  <ErrorBoundary onShowNotification={showNotification}>
                    {renderCurrentView()}
                  </ErrorBoundary>
                </Box>

                {/* Enhanced notification system */}
                <NotificationSystem />

                {/* Fallback for legacy notifications */}
                <Snackbar
                  open={notification.open}
                  autoHideDuration={6000}
                  onClose={handleCloseNotification}
                  anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
                >
                  <Alert
                    onClose={handleCloseNotification}
                    severity={notification.severity}
                    variant="filled"
                    sx={{ width: '100%' }}
                  >
                    {notification.message}
                  </Alert>
                </Snackbar>
              </Box>
            </ThemeProvider>
          </TransferProvider>
        </DeviceProvider>
      </SettingsProvider>
    </ErrorBoundary>
  );
}

export default App;
