const { app, BrowserWindow, ipcMain, dialog, shell } = require('electron');
const path = require('path');
const fs = require('fs-extra');
const isDev = require('electron-is-dev');

// Import services
const ADBService = require('./services/ADBService');
const FileService = require('./services/FileService');
const TransferService = require('./services/TransferService');
const DeviceService = require('./services/DeviceService');
const FileStorageService = require('./services/FileStorageService');
const SettingsService = require('./services/SettingsService');

class AndroidManagerApp {
  constructor() {
    this.mainWindow = null;
    this.services = {};
    this.initializeServices();
  }

  async initializeServices() {
    try {
      // Initialize file storage service
      this.services.storage = new FileStorageService();

      // Initialize other services
      this.services.adb = new ADBService();
      this.services.file = new FileService();
      this.services.transfer = new TransferService(this.services.storage);
      this.services.device = new DeviceService(this.services.adb, this.services.storage);
      this.services.settings = new SettingsService(this.services.storage);

      console.log('All services initialized successfully');
    } catch (error) {
      console.error('Failed to initialize services:', error);
    }
  }

  createMainWindow() {
    this.mainWindow = new BrowserWindow({
      width: 1400,
      height: 900,
      minWidth: 1200,
      minHeight: 700,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        enableRemoteModule: false,
        preload: path.join(__dirname, 'preload.js')
      },
      icon: path.join(__dirname, '../assets/icon.png'),
      titleBarStyle: 'default',
      show: false
    });

    // Store reference globally for services
    global.mainWindow = this.mainWindow;

    // Load the React app
    const startUrl = isDev 
      ? 'http://localhost:3000' 
      : `file://${path.join(__dirname, '../../build/index.html')}`;
    
    this.mainWindow.loadURL(startUrl);

    // Show window when ready
    this.mainWindow.once('ready-to-show', () => {
      this.mainWindow.show();
      
      if (isDev) {
        this.mainWindow.webContents.openDevTools();
      }
    });

    // Handle window closed
    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
    });

    // Handle external links
    this.mainWindow.webContents.setWindowOpenHandler(({ url }) => {
      shell.openExternal(url);
      return { action: 'deny' };
    });
  }

  setupIpcHandlers() {
    // Device management
    ipcMain.handle('device:list', async () => {
      return await this.services.device.listDevices();
    });

    ipcMain.handle('device:connect', async (event, deviceId) => {
      return await this.services.device.connectDevice(deviceId);
    });

    ipcMain.handle('device:disconnect', async (event, deviceId) => {
      return await this.services.device.disconnectDevice(deviceId);
    });

    ipcMain.handle('device:info', async (event, deviceId) => {
      return await this.services.device.getDeviceInfo(deviceId);
    });

    ipcMain.handle('device:getAdbStatus', async () => {
      try {
        const version = await this.services.adb.getVersion();
        return {
          available: this.services.adb.isInitialized,
          version: version,
          path: this.services.adb.adbPath,
          error: null
        };
      } catch (error) {
        return {
          available: false,
          version: null,
          path: this.services.adb.adbPath,
          error: error.message
        };
      }
    });

    ipcMain.handle('device:restartAdb', async () => {
      try {
        await this.services.adb.killServer();
        await this.services.adb.startADBServer();
        return { success: true };
      } catch (error) {
        console.error('Failed to restart ADB:', error);
        throw error;
      }
    });

    // File operations
    ipcMain.handle('file:list', async (event, deviceId, path) => {
      return await this.services.adb.listFiles(deviceId, path);
    });

    ipcMain.handle('file:list-pc', async (event, path) => {
      return await this.services.file.listPCFiles(path);
    });

    ipcMain.handle('file:create-folder', async (event, deviceId, path, name) => {
      return await this.services.adb.createFolder(deviceId, path, name);
    });

    ipcMain.handle('file:delete', async (event, deviceId, paths) => {
      // Handle multiple paths
      const results = [];
      for (const filePath of paths) {
        try {
          const result = await this.services.adb.deleteFile(deviceId, filePath);
          results.push({ path: filePath, success: true });
        } catch (error) {
          results.push({ path: filePath, success: false, error: error.message });
        }
      }
      return { results };
    });

    ipcMain.handle('file:rename', async (event, deviceId, oldPath, newPath) => {
      return await this.services.adb.renameFile(deviceId, oldPath, newPath);
    });

    // Transfer operations
    ipcMain.handle('transfer:upload', async (event, deviceId, localPaths, remotePath) => {
      return await this.services.transfer.uploadFiles(deviceId, localPaths, remotePath);
    });

    ipcMain.handle('transfer:download', async (event, deviceId, remotePaths, localPath) => {
      return await this.services.transfer.downloadFiles(deviceId, remotePaths, localPath);
    });

    ipcMain.handle('transfer:queue', async () => {
      return await this.services.transfer.getQueue();
    });

    ipcMain.handle('transfer:cancel', async (event, transferId) => {
      return await this.services.transfer.cancelTransfer(transferId);
    });

    ipcMain.handle('transfer:history', async () => {
      return await this.services.transfer.getHistory();
    });

    // Application management
    ipcMain.handle('app:list', async (event, deviceId) => {
      return await this.services.device.listApplications(deviceId);
    });

    ipcMain.handle('app:install', async (event, deviceId, apkPath) => {
      return await this.services.device.installApplication(deviceId, apkPath);
    });

    ipcMain.handle('app:uninstall', async (event, deviceId, packageName) => {
      return await this.services.device.uninstallApplication(deviceId, packageName);
    });

    ipcMain.handle('app:backup', async (event, deviceId, packageName, outputPath) => {
      return await this.services.device.backupApplication(deviceId, packageName, outputPath);
    });

    ipcMain.handle('app:launch', async (event, deviceId, packageName) => {
      return await this.services.device.launchApp(deviceId, packageName);
    });

    // Device Tools
    ipcMain.handle('device:takeScreenshot', async (event, deviceId) => {
      return await this.services.device.takeScreenshot(deviceId);
    });

    ipcMain.handle('device:saveScreenshot', async (event, imagePath) => {
      return await this.services.device.saveScreenshot(imagePath);
    });

    ipcMain.handle('device:startLogcat', async (event, deviceId, options) => {
      return await this.services.device.startLogcat(deviceId, options);
    });

    ipcMain.handle('device:stopLogcat', async (event, deviceId) => {
      return await this.services.device.stopLogcat(deviceId);
    });

    ipcMain.handle('device:saveLogcat', async (event, logs) => {
      return await this.services.device.saveLogcat(logs);
    });

    ipcMain.handle('device:executeShell', async (event, deviceId, command) => {
      return await this.services.device.executeShell(deviceId, command);
    });

    ipcMain.handle('device:reboot', async (event, deviceId, mode) => {
      return await this.services.device.rebootDevice(deviceId, mode);
    });

    ipcMain.handle('device:toggleWifi', async (event, deviceId) => {
      return await this.services.device.toggleWifi(deviceId);
    });

    ipcMain.handle('device:getDetailedInfo', async (event, deviceId) => {
      return await this.services.device.getDetailedDeviceInfo(deviceId);
    });

    // File operations - PC
    ipcMain.handle('file:createFolderPC', async (event, dirPath, folderName) => {
      return await this.services.file.createFolder(dirPath, folderName);
    });

    ipcMain.handle('file:deletePC', async (event, filePaths) => {
      return await this.services.file.deleteFiles(filePaths);
    });

    ipcMain.handle('file:renamePC', async (event, oldPath, newPath) => {
      return await this.services.file.renameFile(oldPath, newPath);
    });

    ipcMain.handle('file:copyPC', async (event, sourcePaths, destinationDir) => {
      return await this.services.file.copyFiles(sourcePaths, destinationDir);
    });

    ipcMain.handle('file:getInfo', async (event, filePath) => {
      return await this.services.file.getFileInfo(filePath);
    });

    ipcMain.handle('file:search', async (event, searchDir, query, options) => {
      return await this.services.file.searchFiles(searchDir, query, options);
    });

    ipcMain.handle('file:getDrives', async () => {
      return this.services.file.getSystemDrives();
    });

    ipcMain.handle('file:getPlatformInfo', async () => {
      return {
        platform: process.platform,
        separator: require('path').sep
      };
    });

    // System dialogs
    ipcMain.handle('dialog:open-file', async (event, options) => {
      const result = await dialog.showOpenDialog(this.mainWindow, options);
      return result;
    });

    ipcMain.handle('dialog:save-file', async (event, options) => {
      const result = await dialog.showSaveDialog(this.mainWindow, options);
      return result;
    });

    // Advanced features
    ipcMain.handle('device:screenshot', async (event, deviceId) => {
      return await this.services.device.takeScreenshot(deviceId);
    });

    ipcMain.handle('device:logcat', async (event, deviceId, options) => {
      return await this.services.device.getLogcat(deviceId, options);
    });

    // Settings - Enhanced
    ipcMain.handle('settings:get', async () => {
      return await this.services.settings.getSettings();
    });

    ipcMain.handle('settings:set', async (event, settings) => {
      return await this.services.settings.saveSettings(settings);
    });

    ipcMain.handle('settings:reset', async () => {
      return await this.services.settings.resetSettings();
    });

    ipcMain.handle('settings:export', async () => {
      return await this.services.settings.exportSettings();
    });

    ipcMain.handle('settings:import', async (event, settingsData) => {
      return await this.services.settings.importSettings(settingsData);
    });

    ipcMain.handle('settings:getSetting', async (event, key) => {
      return await this.services.settings.getSetting(key);
    });

    ipcMain.handle('settings:setSetting', async (event, key, value) => {
      return await this.services.settings.setSetting(key, value);
    });

    ipcMain.handle('settings:validate', async (event, settings) => {
      return await this.services.settings.validateSettings(settings);
    });

    ipcMain.handle('settings:backup', async () => {
      return await this.services.settings.backupSettings();
    });

    ipcMain.handle('settings:restore', async (event, backupPath) => {
      return await this.services.settings.restoreSettings(backupPath);
    });

    ipcMain.handle('settings:getInfo', async () => {
      return await this.services.settings.getSettingsInfo();
    });

    // Transfer operations - Enhanced
    ipcMain.handle('transfer:pause', async (event, transferId) => {
      return await this.services.transfer.pauseTransfer(transferId);
    });

    ipcMain.handle('transfer:resume', async (event, transferId) => {
      return await this.services.transfer.resumeTransfer(transferId);
    });

    ipcMain.handle('transfer:retry', async (event, transferId) => {
      return await this.services.transfer.retryTransfer(transferId);
    });

    ipcMain.handle('transfer:clearHistory', async () => {
      return await this.services.transfer.clearHistory();
    });

    ipcMain.handle('transfer:getStats', async () => {
      return await this.services.transfer.getTransferStats();
    });
  }

  async initialize() {
    await this.initializeServices();
    this.setupIpcHandlers();
    this.createMainWindow();
  }
}

// App event handlers
app.whenReady().then(async () => {
  const androidManager = new AndroidManagerApp();
  await androidManager.initialize();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      androidManager.createMainWindow();
    }
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// Security: Prevent new window creation
app.on('web-contents-created', (event, contents) => {
  contents.on('new-window', (event, navigationUrl) => {
    event.preventDefault();
    shell.openExternal(navigationUrl);
  });
});
