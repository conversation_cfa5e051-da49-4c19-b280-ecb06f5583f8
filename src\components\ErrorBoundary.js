import React from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  Alert,
  Alert<PERSON><PERSON>le,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  <PERSON>ack,
  Chip
} from '@mui/material';
import {
  Error as ErrorIcon,
  ExpandMore as ExpandMoreIcon,
  Refresh as RefreshIcon,
  Home as HomeIcon,
  BugReport as BugReportIcon
} from '@mui/icons-material';
import { logError, getErrorMessage, ErrorSeverity } from '../utils/errorHandler';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null
    };
  }

  static getDerivedStateFromError(error) {
    return {
      hasError: true,
      error,
      errorId: Date.now().toString(36) + Math.random().toString(36).substr(2)
    };
  }

  componentDidCatch(error, errorInfo) {
    this.setState({ errorInfo });
    
    // Log the error
    logError(error, 'React Error Boundary', {
      componentStack: errorInfo.componentStack,
      errorBoundary: this.constructor.name,
      props: this.props
    });
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null
    });
  };

  handleGoHome = () => {
    if (this.props.onNavigateHome) {
      this.props.onNavigateHome();
    }
    this.handleRetry();
  };

  handleReportError = () => {
    const errorReport = {
      error: this.state.error?.message,
      stack: this.state.error?.stack,
      componentStack: this.state.errorInfo?.componentStack,
      errorId: this.state.errorId,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    // Copy error report to clipboard
    navigator.clipboard.writeText(JSON.stringify(errorReport, null, 2))
      .then(() => {
        if (this.props.onShowNotification) {
          this.props.onShowNotification('Error report copied to clipboard', 'info');
        }
      })
      .catch(() => {
        console.log('Error report:', errorReport);
      });
  };

  getSeverityColor = (severity) => {
    switch (severity) {
      case ErrorSeverity.CRITICAL:
        return 'error';
      case ErrorSeverity.HIGH:
        return 'warning';
      case ErrorSeverity.MEDIUM:
        return 'info';
      case ErrorSeverity.LOW:
        return 'success';
      default:
        return 'default';
    }
  };

  render() {
    if (this.state.hasError) {
      const errorDetails = getErrorMessage(this.state.error, 'React Component Error');
      const severityColor = this.getSeverityColor(errorDetails.severity);

      return (
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: '400px',
            p: 3
          }}
        >
          <Paper
            elevation={3}
            sx={{
              p: 4,
              maxWidth: 600,
              width: '100%',
              textAlign: 'center'
            }}
          >
            <ErrorIcon
              sx={{
                fontSize: 64,
                color: 'error.main',
                mb: 2
              }}
            />

            <Typography variant="h4" gutterBottom color="error">
              Oops! Something went wrong
            </Typography>

            <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
              We encountered an unexpected error. Don't worry, your data is safe.
            </Typography>

            <Alert severity={severityColor} sx={{ mb: 3, textAlign: 'left' }}>
              <AlertTitle>{errorDetails.title}</AlertTitle>
              {errorDetails.message}
            </Alert>

            <Stack direction="row" spacing={2} justifyContent="center" sx={{ mb: 3 }}>
              <Button
                variant="contained"
                startIcon={<RefreshIcon />}
                onClick={this.handleRetry}
                color="primary"
              >
                Try Again
              </Button>

              {this.props.onNavigateHome && (
                <Button
                  variant="outlined"
                  startIcon={<HomeIcon />}
                  onClick={this.handleGoHome}
                >
                  Go Home
                </Button>
              )}

              <Button
                variant="outlined"
                startIcon={<BugReportIcon />}
                onClick={this.handleReportError}
                color="secondary"
              >
                Report Error
              </Button>
            </Stack>

            {errorDetails.suggestions && errorDetails.suggestions.length > 0 && (
              <Alert severity="info" sx={{ mb: 3, textAlign: 'left' }}>
                <AlertTitle>Suggestions</AlertTitle>
                <ul style={{ margin: 0, paddingLeft: 20 }}>
                  {errorDetails.suggestions.map((suggestion, index) => (
                    <li key={index}>{suggestion}</li>
                  ))}
                </ul>
              </Alert>
            )}

            <Accordion sx={{ textAlign: 'left' }}>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography variant="subtitle2">
                  Technical Details
                  <Chip
                    label={`Error ID: ${this.state.errorId}`}
                    size="small"
                    sx={{ ml: 2 }}
                  />
                </Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Typography variant="body2" component="pre" sx={{ 
                  whiteSpace: 'pre-wrap',
                  wordBreak: 'break-word',
                  fontSize: '0.75rem',
                  backgroundColor: 'grey.100',
                  p: 2,
                  borderRadius: 1,
                  maxHeight: 200,
                  overflow: 'auto'
                }}>
                  {this.state.error?.stack || this.state.error?.message || 'No error details available'}
                </Typography>
              </AccordionDetails>
            </Accordion>
          </Paper>
        </Box>
      );
    }

    return this.props.children;
  }
}

// Higher-order component for wrapping components with error boundary
export const withErrorBoundary = (Component, errorBoundaryProps = {}) => {
  return function WrappedComponent(props) {
    return (
      <ErrorBoundary {...errorBoundaryProps}>
        <Component {...props} />
      </ErrorBoundary>
    );
  };
};

// Hook for error handling in functional components
export const useErrorHandler = () => {
  const [error, setError] = React.useState(null);

  const handleError = React.useCallback((error, context = '') => {
    logError(error, context);
    setError(error);
  }, []);

  const clearError = React.useCallback(() => {
    setError(null);
  }, []);

  const retryOperation = React.useCallback(async (operation, ...args) => {
    try {
      clearError();
      return await operation(...args);
    } catch (error) {
      handleError(error, 'Retry operation');
      throw error;
    }
  }, [handleError, clearError]);

  return {
    error,
    handleError,
    clearError,
    retryOperation,
    hasError: !!error
  };
};

export default ErrorBoundary;
