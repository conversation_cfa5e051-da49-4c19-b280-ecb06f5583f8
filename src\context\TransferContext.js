import React, { createContext, useContext, useReducer, useEffect } from 'react';

// Initial state
const initialState = {
  activeTransfers: [],
  transferQueue: [],
  transferHistory: [],
  loading: false,
  error: null
};

// Action types
const ActionTypes = {
  SET_LOADING: 'SET_LOADING',
  SET_ACTIVE_TRANSFERS: 'SET_ACTIVE_TRANSFERS',
  SET_TRANSFER_QUEUE: 'SET_TRANSFER_QUEUE',
  SET_TRANSFER_HISTORY: 'SET_TRANSFER_HISTORY',
  ADD_TRANSFER: 'ADD_TRANSFER',
  UPDATE_TRANSFER: 'UPDATE_TRANSFER',
  REMOVE_TRANSFER: 'REMOVE_TRANSFER',
  SET_ERROR: 'SET_ERROR',
  CLEAR_ERROR: 'CLEAR_ERROR'
};

// Reducer
function transferReducer(state, action) {
  switch (action.type) {
    case ActionTypes.SET_LOADING:
      return { ...state, loading: action.payload };
    
    case ActionTypes.SET_ACTIVE_TRANSFERS:
      return { ...state, activeTransfers: action.payload };
    
    case ActionTypes.SET_TRANSFER_QUEUE:
      return { ...state, transferQueue: action.payload };
    
    case ActionTypes.SET_TRANSFER_HISTORY:
      return { ...state, transferHistory: action.payload };
    
    case ActionTypes.ADD_TRANSFER:
      return { 
        ...state, 
        activeTransfers: [...state.activeTransfers, action.payload]
      };
    
    case ActionTypes.UPDATE_TRANSFER:
      const updatedTransfers = state.activeTransfers.map(transfer =>
        transfer.id === action.payload.id ? { ...transfer, ...action.payload } : transfer
      );
      
      return { ...state, activeTransfers: updatedTransfers };
    
    case ActionTypes.REMOVE_TRANSFER:
      return {
        ...state,
        activeTransfers: state.activeTransfers.filter(transfer => transfer.id !== action.payload)
      };
    
    case ActionTypes.SET_ERROR:
      return { ...state, error: action.payload, loading: false };
    
    case ActionTypes.CLEAR_ERROR:
      return { ...state, error: null };
    
    default:
      return state;
  }
}

// Context
const TransferContext = createContext();

// Provider component
export function TransferProvider({ children }) {
  const [state, dispatch] = useReducer(transferReducer, initialState);

  // Actions
  const actions = {
    setLoading: (loading) => {
      dispatch({ type: ActionTypes.SET_LOADING, payload: loading });
    },

    setError: (error) => {
      dispatch({ type: ActionTypes.SET_ERROR, payload: error });
    },

    clearError: () => {
      dispatch({ type: ActionTypes.CLEAR_ERROR });
    },

    uploadFiles: async (deviceId, localPaths, remotePath) => {
      try {
        if (!window.electronAPI?.transfer?.upload) {
          console.warn('Transfer API not available - running in development mode');
          return { success: false, message: 'Transfer API not available' };
        }
        const result = await window.electronAPI.transfer.upload(deviceId, localPaths, remotePath);
        dispatch({ type: ActionTypes.ADD_TRANSFER, payload: result.transfer });
        return result;
      } catch (error) {
        console.error('Failed to start upload:', error);
        dispatch({ type: ActionTypes.SET_ERROR, payload: error.message });
        throw error;
      }
    },

    downloadFiles: async (deviceId, remotePaths, localPath) => {
      try {
        if (!window.electronAPI?.transfer?.download) {
          console.warn('Transfer API not available - running in development mode');
          return { success: false, message: 'Transfer API not available' };
        }
        const result = await window.electronAPI.transfer.download(deviceId, remotePaths, localPath);
        dispatch({ type: ActionTypes.ADD_TRANSFER, payload: result.transfer });
        return result;
      } catch (error) {
        console.error('Failed to start download:', error);
        dispatch({ type: ActionTypes.SET_ERROR, payload: error.message });
        throw error;
      }
    },

    cancelTransfer: async (transferId) => {
      try {
        if (!window.electronAPI?.transfer?.cancel) {
          console.warn('Transfer API not available - running in development mode');
          return { success: false, message: 'Transfer API not available' };
        }
        const result = await window.electronAPI.transfer.cancel(transferId);
        if (result.success) {
          dispatch({ type: ActionTypes.REMOVE_TRANSFER, payload: transferId });
        }
        return result;
      } catch (error) {
        console.error('Failed to cancel transfer:', error);
        dispatch({ type: ActionTypes.SET_ERROR, payload: error.message });
        throw error;
      }
    },

    refreshQueue: async () => {
      try {
        if (!window.electronAPI?.transfer?.getQueue) {
          console.warn('Transfer API not available - running in development mode');
          return;
        }
        const queue = await window.electronAPI.transfer.getQueue();
        dispatch({ type: ActionTypes.SET_TRANSFER_QUEUE, payload: queue });
      } catch (error) {
        console.error('Failed to refresh transfer queue:', error);
        dispatch({ type: ActionTypes.SET_ERROR, payload: error.message });
      }
    },

    refreshHistory: async () => {
      try {
        if (!window.electronAPI?.transfer?.getHistory) {
          console.warn('Transfer API not available - running in development mode');
          return;
        }
        const history = await window.electronAPI.transfer.getHistory();
        dispatch({ type: ActionTypes.SET_TRANSFER_HISTORY, payload: history });
      } catch (error) {
        console.error('Failed to refresh transfer history:', error);
        dispatch({ type: ActionTypes.SET_ERROR, payload: error.message });
      }
    },

    updateTransfer: (transfer) => {
      dispatch({ type: ActionTypes.UPDATE_TRANSFER, payload: transfer });
    }
  };

  // Setup event listeners for transfer events
  useEffect(() => {
    // Check if electronAPI is available
    if (!window.electronAPI?.transfer) {
      console.warn('Transfer API not available - running in development mode');
      return;
    }

    const removeProgressListener = window.electronAPI.transfer.onProgress((data) => {
      actions.updateTransfer(data);
    });

    const removeCompleteListener = window.electronAPI.transfer.onComplete((data) => {
      actions.updateTransfer({ ...data, status: 'completed' });
      // Refresh history to include completed transfer
      actions.refreshHistory();
    });

    const removeErrorListener = window.electronAPI.transfer.onError((data) => {
      actions.updateTransfer({ ...data, status: 'failed' });
    });

    // Initial load
    actions.refreshQueue();
    actions.refreshHistory();

    return () => {
      removeProgressListener();
      removeCompleteListener();
      removeErrorListener();
    };
  }, []);

  const value = {
    ...state,
    ...actions
  };

  return (
    <TransferContext.Provider value={value}>
      {children}
    </TransferContext.Provider>
  );
}

// Hook to use transfer context
export function useTransfer() {
  const context = useContext(TransferContext);
  if (!context) {
    throw new Error('useTransfer must be used within a TransferProvider');
  }
  return context;
}

export default TransferContext;
