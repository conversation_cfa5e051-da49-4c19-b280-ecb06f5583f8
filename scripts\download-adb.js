const https = require('https');
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// ADB download URLs (Android SDK Platform Tools)
const ADB_DOWNLOADS = {
  win32: 'https://dl.google.com/android/repository/platform-tools_r34.0.5-windows.zip',
  darwin: 'https://dl.google.com/android/repository/platform-tools_r34.0.5-darwin.zip',
  linux: 'https://dl.google.com/android/repository/platform-tools_r34.0.5-linux.zip'
};

const RESOURCES_DIR = path.join(__dirname, '..', 'resources', 'adb');

async function downloadFile(url, outputPath) {
  return new Promise((resolve, reject) => {
    console.log(`Downloading ${url}...`);
    
    const file = fs.createWriteStream(outputPath);
    
    https.get(url, (response) => {
      if (response.statusCode === 302 || response.statusCode === 301) {
        // Handle redirect
        return downloadFile(response.headers.location, outputPath)
          .then(resolve)
          .catch(reject);
      }
      
      if (response.statusCode !== 200) {
        reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`));
        return;
      }
      
      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        console.log(`Downloaded: ${outputPath}`);
        resolve();
      });
      
      file.on('error', (err) => {
        fs.unlink(outputPath, () => {}); // Delete partial file
        reject(err);
      });
    }).on('error', reject);
  });
}

function extractZip(zipPath, extractPath) {
  console.log(`Extracting ${zipPath} to ${extractPath}...`);
  
  try {
    // Create extraction directory
    if (!fs.existsSync(extractPath)) {
      fs.mkdirSync(extractPath, { recursive: true });
    }
    
    // Use different extraction methods based on platform
    if (process.platform === 'win32') {
      // Use PowerShell on Windows
      execSync(`powershell -command "Expand-Archive -Path '${zipPath}' -DestinationPath '${extractPath}' -Force"`, {
        stdio: 'inherit'
      });
    } else {
      // Use unzip on Unix-like systems
      execSync(`unzip -o "${zipPath}" -d "${extractPath}"`, {
        stdio: 'inherit'
      });
    }
    
    console.log(`Extracted: ${zipPath}`);
  } catch (error) {
    console.error(`Failed to extract ${zipPath}:`, error.message);
    throw error;
  }
}

function copyAdbBinaries(platform, extractPath, targetPath) {
  console.log(`Copying ADB binaries for ${platform}...`);
  
  const platformToolsPath = path.join(extractPath, 'platform-tools');
  
  if (!fs.existsSync(targetPath)) {
    fs.mkdirSync(targetPath, { recursive: true });
  }
  
  switch (platform) {
    case 'win32':
      // Copy Windows binaries
      fs.copyFileSync(
        path.join(platformToolsPath, 'adb.exe'),
        path.join(targetPath, 'adb.exe')
      );
      fs.copyFileSync(
        path.join(platformToolsPath, 'AdbWinApi.dll'),
        path.join(targetPath, 'AdbWinApi.dll')
      );
      fs.copyFileSync(
        path.join(platformToolsPath, 'AdbWinUsbApi.dll'),
        path.join(targetPath, 'AdbWinUsbApi.dll')
      );
      break;
      
    case 'darwin':
    case 'linux':
      // Copy Unix binaries
      fs.copyFileSync(
        path.join(platformToolsPath, 'adb'),
        path.join(targetPath, 'adb')
      );
      // Make executable
      fs.chmodSync(path.join(targetPath, 'adb'), 0o755);
      break;
  }
  
  console.log(`Copied ADB binaries for ${platform}`);
}

async function downloadAndExtractAdb() {
  console.log('Starting ADB binary download and extraction...');
  
  // Create resources directory
  if (!fs.existsSync(RESOURCES_DIR)) {
    fs.mkdirSync(RESOURCES_DIR, { recursive: true });
  }
  
  const tempDir = path.join(__dirname, '..', 'temp');
  if (!fs.existsSync(tempDir)) {
    fs.mkdirSync(tempDir, { recursive: true });
  }
  
  try {
    for (const [platform, url] of Object.entries(ADB_DOWNLOADS)) {
      console.log(`\n--- Processing ${platform} ---`);
      
      const zipPath = path.join(tempDir, `platform-tools-${platform}.zip`);
      const extractPath = path.join(tempDir, platform);
      const targetPath = path.join(RESOURCES_DIR, platform);
      
      // Skip if binaries already exist
      const expectedBinary = platform === 'win32' ? 'adb.exe' : 'adb';
      if (fs.existsSync(path.join(targetPath, expectedBinary))) {
        console.log(`ADB binaries for ${platform} already exist, skipping...`);
        continue;
      }
      
      // Download
      await downloadFile(url, zipPath);
      
      // Extract
      extractZip(zipPath, extractPath);
      
      // Copy binaries
      copyAdbBinaries(platform, extractPath, targetPath);
      
      // Cleanup
      fs.unlinkSync(zipPath);
      fs.rmSync(extractPath, { recursive: true, force: true });
    }
    
    // Cleanup temp directory
    fs.rmSync(tempDir, { recursive: true, force: true });
    
    console.log('\n✅ ADB binaries downloaded and extracted successfully!');
    
  } catch (error) {
    console.error('\n❌ Failed to download ADB binaries:', error.message);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  downloadAndExtractAdb();
}

module.exports = { downloadAndExtractAdb };
