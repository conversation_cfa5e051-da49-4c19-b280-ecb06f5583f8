const fs = require('fs-extra');
const path = require('path');
const os = require('os');

class FileStorageService {
  constructor() {
    this.dataDir = path.join(os.homedir(), '.android-manager');
    this.ensureDataDirectory();
  }

  async ensureDataDirectory() {
    try {
      await fs.ensureDir(this.dataDir);
    } catch (error) {
      console.error('Failed to create data directory:', error);
    }
  }

  getFilePath(filename) {
    return path.join(this.dataDir, filename);
  }

  async readJSON(filename, defaultValue = {}) {
    try {
      const filePath = this.getFilePath(filename);
      const exists = await fs.pathExists(filePath);
      
      if (!exists) {
        return defaultValue;
      }

      const data = await fs.readFile(filePath, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      console.error(`Failed to read ${filename}:`, error);
      return defaultValue;
    }
  }

  async writeJSON(filename, data) {
    try {
      const filePath = this.getFilePath(filename);
      await fs.writeFile(filePath, JSON.stringify(data, null, 2), 'utf8');
      return true;
    } catch (error) {
      console.error(`Failed to write ${filename}:`, error);
      return false;
    }
  }

  async appendToArray(filename, item, maxItems = 1000) {
    try {
      const data = await this.readJSON(filename, []);
      data.unshift(item); // Add to beginning
      
      // Keep only the most recent items
      if (data.length > maxItems) {
        data.splice(maxItems);
      }
      
      await this.writeJSON(filename, data);
      return true;
    } catch (error) {
      console.error(`Failed to append to ${filename}:`, error);
      return false;
    }
  }

  async updateArrayItem(filename, predicate, updates) {
    try {
      const data = await this.readJSON(filename, []);
      const index = data.findIndex(predicate);
      
      if (index !== -1) {
        data[index] = { ...data[index], ...updates };
        await this.writeJSON(filename, data);
        return true;
      }
      
      return false;
    } catch (error) {
      console.error(`Failed to update item in ${filename}:`, error);
      return false;
    }
  }

  async removeArrayItem(filename, predicate) {
    try {
      const data = await this.readJSON(filename, []);
      const filteredData = data.filter(item => !predicate(item));
      
      if (filteredData.length !== data.length) {
        await this.writeJSON(filename, filteredData);
        return true;
      }
      
      return false;
    } catch (error) {
      console.error(`Failed to remove item from ${filename}:`, error);
      return false;
    }
  }

  async findArrayItems(filename, predicate) {
    try {
      const data = await this.readJSON(filename, []);
      return data.filter(predicate);
    } catch (error) {
      console.error(`Failed to find items in ${filename}:`, error);
      return [];
    }
  }

  async getAllArrayItems(filename) {
    return await this.readJSON(filename, []);
  }

  async updateSetting(key, value) {
    try {
      const settings = await this.readJSON('settings.json', {});
      settings[key] = {
        value: value,
        updated_at: new Date().toISOString()
      };
      await this.writeJSON('settings.json', settings);
      return true;
    } catch (error) {
      console.error('Failed to update setting:', error);
      return false;
    }
  }

  async getSetting(key, defaultValue = null) {
    try {
      const settings = await this.readJSON('settings.json', {});
      return settings[key] ? settings[key].value : defaultValue;
    } catch (error) {
      console.error('Failed to get setting:', error);
      return defaultValue;
    }
  }

  async getAllSettings() {
    try {
      const settings = await this.readJSON('settings.json', {});
      const result = {};
      
      for (const [key, data] of Object.entries(settings)) {
        result[key] = data.value;
      }
      
      return result;
    } catch (error) {
      console.error('Failed to get all settings:', error);
      return {};
    }
  }

  async clearFile(filename) {
    try {
      const filePath = this.getFilePath(filename);
      await fs.remove(filePath);
      return true;
    } catch (error) {
      console.error(`Failed to clear ${filename}:`, error);
      return false;
    }
  }

  async getFileStats(filename) {
    try {
      const filePath = this.getFilePath(filename);
      const exists = await fs.pathExists(filePath);
      
      if (!exists) {
        return null;
      }
      
      const stats = await fs.stat(filePath);
      return {
        size: stats.size,
        created: stats.birthtime,
        modified: stats.mtime
      };
    } catch (error) {
      console.error(`Failed to get stats for ${filename}:`, error);
      return null;
    }
  }
}

module.exports = FileStorageService;
