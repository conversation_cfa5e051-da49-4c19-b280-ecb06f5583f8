# ADB Binaries

This directory contains the Android Debug Bridge (ADB) binaries for different platforms.

## Directory Structure

```
adb/
├── win32/
│   ├── adb.exe
│   ├── AdbWinApi.dll
│   └── AdbWinUsbApi.dll
├── darwin/
│   └── adb
└── linux/
    └── adb
```

## Binary Sources

These binaries are extracted from the official Android SDK Platform Tools:
- **Windows**: platform-tools_r34.0.5-windows.zip
- **macOS**: platform-tools_r34.0.5-darwin.zip  
- **Linux**: platform-tools_r34.0.5-linux.zip

## License

These binaries are distributed under the Android Software Development Kit License Agreement.

## Usage

The application automatically selects the correct binary based on the operating system:
- Windows: `adb.exe` + required DLLs
- macOS: `adb` (Intel/Apple Silicon universal binary)
- Linux: `adb` (x64 binary)

## Updates

To update ADB binaries:
1. Download the latest platform-tools from https://developer.android.com/studio/releases/platform-tools
2. Extract the appropriate binaries for each platform
3. Replace the files in the respective directories
4. Update the version information in this README
